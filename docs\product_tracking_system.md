# Универсална система за проследяване на синхронизирани продукти

## Описание

Имплементирана е универсална система за автоматично проследяване на синхронизирани продукти с цел автоматично нулиране на бройките на продукти, които вече не участват в синхронизацията. Системата е рефакторирана за максимална преизползваемост и може да се използва от всички конектори без дублиране на код.

## Архитектурни подобрения (v2.1)

### Централизирана логика
- Всички методи за проследяване са преместени в основния модел (`multi_feed_syncer.php`)
- Конекторите вече не съдържат дублиран код
- Автоматично откриване на поддръжка за проследяване

### Опростена интеграция
- Автоматично инициализиране в началото на `doSync()`
- Автоматично финализиране в края на `doSync()`
- Няма нужда от ръчни извиквания в конекторите

### Корекция за batch обработка (v2.1)
- Решен проблем с неправилното нулиране на бройки при batch обработка
- Добавен флаг за предотвратяване на множествено инициализиране
- Подобрено логиране за диагностика и проследяване на процеса

## Структура на базата данни

### Таблица `multi_feed_syncer_product_to_connector`

```sql
CREATE TABLE IF NOT EXISTS `oc_multi_feed_syncer_product_to_connector` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `mfsc_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_connector` (`product_id`, `mfsc_id`),
  KEY `mfsc_id` (`mfsc_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

**Полета:**
- `id` - Уникален идентификатор на записа
- `product_id` - ID на продукта от OpenCart (връзка към oc_product)
- `mfsc_id` - ID на конектора (връзка към multi_feed_syncer_connectors)
- `date_added` - Дата на първо добавяне на записа
- `date_modified` - Дата на последна модификация на записа

## Логика на работа (v2.1)

### 1. Автоматично инициализиране

В началото на `doSync()` метода в основния модел се извиква автоматично `_initializeProductTracking($mfsc_id)`:
- Проверява се дали вече е инициализирано за тази синхронизация (`self::$product_tracking_initialized`)
- Проверява се дали конекторът поддържа проследяване (в момента само eOffice)
- Инициализира се статичен масив `self::$current_sync_product_ids = []` само при първото извикване
- Задава се флаг `self::$product_tracking_enabled = true/false`
- Маркира се като инициализирано (`self::$product_tracking_initialized = true`)
- Записва се лог за започване на проследяването

### 2. Автоматично проследяване по време на синхронизация

При всяка обработка на продукт (независимо дали е нов или съществуващ):
- В основния модел се извиква автоматично `_trackSyncedProductInConnector($product_id, $mfsc_id)`
- Ако проследяването е активирано, `product_id` се добавя в статичния масив
- Продуктите се акумулират от всички batch порции
- Логира се прогреса на всеки 100-ти продукт
- Използва се `array_unique()` за избягване на дублирани записи
- Няма нужда от код в конекторите

### 3. Автоматично финализиране в края на синхронизацията

След приключване на обработката на всички продукти се извиква автоматично `_finalizeProductTracking($mfsc_id, $processed_info)` в основния модел:

### 4. Ресетиране за следващата синхронизация

След финализирането се извиква `_resetProductTracking()` за подготовка за следващата синхронизация:
- Нулира се `self::$current_sync_product_ids`
- Ресетират се флаговете `self::$product_tracking_enabled` и `self::$product_tracking_initialized`

#### 3.1. Зареждане на предишни синхронизации
```sql
SELECT `product_id` 
FROM `oc_multi_feed_syncer_product_to_connector` 
WHERE `mfsc_id` = '{mfsc_id}'
```

#### 3.2. Намиране на изчезнали продукти
```php
$disappeared_products = array_diff($previous_synced_products, self::$current_sync_product_ids);
```

#### 3.3. Нулиране на бройки
За изчезналите продукти се изпълнява:
```sql
UPDATE `oc_product` 
SET `quantity` = 0, `date_modified` = NOW() 
WHERE `product_id` IN ({product_ids})
```

#### 3.4. Почистване на записи
```sql
DELETE FROM `oc_multi_feed_syncer_product_to_connector` 
WHERE `product_id` IN ({disappeared_product_ids}) 
AND `mfsc_id` = '{mfsc_id}'
```

#### 3.5. Актуализиране на текущи записи
```sql
INSERT INTO `oc_multi_feed_syncer_product_to_connector` 
(`product_id`, `mfsc_id`) 
VALUES {values}
ON DUPLICATE KEY UPDATE `date_modified` = NOW()
```

## Технически детайли

### Оптимизация на производителността
- Използват се batch SQL операции за всички операции
- Минимизират се SQL заявките чрез групиране на операциите
- Използва се `ON DUPLICATE KEY UPDATE` за ефективно актуализиране

### Логиране
Всички операции се логват в:
- Основния лог на синхронизацията
- Специализиран лог файл `mfs_product_tracking.log`

### Обработка на грешки
- Всички операции са обградени с try-catch блокове
- При грешка се записва подробна информация в лога
- Грешките не прекъсват основната синхронизация

## Файлове с промени (v2.0)

### Основен модел (`admin/model/extension/module/multi_feed_syncer.php`)
- Добавена таблица в `install()` метода
- Добавено изтриване на таблица в `uninstall()` метода
- Добавени статични променливи:
  - `self::$current_sync_product_ids` - масив за проследяване
  - `self::$product_tracking_enabled` - флаг за активиране
- Добавени методи:
  - `_initializeProductTracking($mfsc_id)` - автоматично инициализиране
  - `_trackSyncedProduct($product_id)` - добавяне на продукт
  - `_finalizeProductTracking($mfsc_id, &$processed_info)` - финализиране
  - `_zeroQuantityForProducts($product_ids, &$processed_info)` - нулиране на бройки
  - `_removeProductsFromTracking($product_ids, $mfsc_id, &$processed_info)` - премахване от проследяване
  - `_updateProductTracking($product_ids, $mfsc_id, &$processed_info)` - актуализиране на проследяване
  - `_trackSyncedProductInConnector($product_id, $mfsc_id)` - опростен метод за извикване
  - `getConnectorById($mfsc_id)` - помощен метод
- Автоматично извикване в `doSync()`:
  - `_initializeProductTracking()` в началото
  - `_finalizeProductTracking()` в края
- Автоматично извикване на проследяването в `_insertRelatedProductDataForChunk()` и `_batchUpdateProducts()`

### eOffice конектор (`admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`)
- **ПРЕМАХНАТИ** всички методи за проследяване (преместени в основния модел)
- **ПРЕМАХНАТА** променливата `$current_sync_product_ids`
- **ПРЕМАХНАТИ** ръчните извиквания на инициализация и финализиране
- Конекторът вече е напълно опростен и не съдържа логика за проследяване

### SQL файл (`sql/multi_feed_syncer_product_to_connector.sql`)
- Създаден SQL файл за ръчно създаване на таблицата

## Тестване

За тестване на функционалността:
1. Стартирайте синхронизация с eOffice конектора
2. Проверете логовете в `mfs_product_tracking.log`
3. Проверете записите в таблицата `multi_feed_syncer_product_to_connector`
4. Симулирайте изчезване на продукти и проверете дали бройките се нулират

## Предимства на рефакторираната архитектура (v2.0)

### ✅ Преизползваемост
- Един код за всички конектори
- Няма дублиране на логика
- Лесно добавяне на нови конектори

### ✅ Автоматизация
- Автоматично откриване на поддръжка
- Автоматично инициализиране и финализиране
- Няма нужда от ръчни извиквания

### ✅ Поддръжка
- Централизирана логика в основния модел
- По-лесно дебъгване и тестване
- Единно място за промени

### ✅ Съвместимост
- Работи само с поддържани конектори (eOffice)
- Не влияе на други конектори
- Обратна съвместимост

## Добавяне на поддръжка за нови конектори

За да добавите поддръжка за нов конектор:

1. **Модифицирайте `_initializeProductTracking()`** в основния модел:
```php
// Добавете новия connector_key в проверката
$supported_connectors = ['eoffice', 'new_connector_key'];
self::$product_tracking_enabled = in_array($connector_key, $supported_connectors);
```

2. **Готово!** Няма нужда от промени в конектора - всичко работи автоматично.

## Бъдещи подобрения

- Конфигурационен файл за поддържани конектори
- Добавяне на статистики в интерфейса за броя нулирани продукти
- Възможност за възстановяване на бройки при повторно появяване на продукти
- Опционално изключване на функционалността чрез настройки
