# Система за проследяване на синхронизирани продукти

## Описание

Имплементирана е система за автоматично проследяване на синхронизирани продукти в eOffice конектора с цел автоматично нулиране на бройките на продукти, които вече не участват в синхронизацията.

## Структура на базата данни

### Таблица `multi_feed_syncer_product_to_connector`

```sql
CREATE TABLE IF NOT EXISTS `oc_multi_feed_syncer_product_to_connector` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `mfsc_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_connector` (`product_id`, `mfsc_id`),
  KEY `mfsc_id` (`mfsc_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

**Полета:**
- `id` - Уникален идентификатор на записа
- `product_id` - ID на продукта от OpenCart (връзка към oc_product)
- `mfsc_id` - ID на конектора (връзка към multi_feed_syncer_connectors)
- `date_added` - Дата на първо добавяне на записа
- `date_modified` - Дата на последна модификация на записа

## Логика на работа

### 1. Инициализация на проследяването

В началото на синхронизацията в eOffice конектора се извиква `_initializeProductTracking($mfsc_id)`:
- Инициализира се празен масив `$current_sync_product_ids = []`
- Записва се лог за започване на проследяването

### 2. Проследяване по време на синхронизация

При всяка обработка на продукт (независимо дали е нов или съществуващ):
- В основния модел се извиква `_trackSyncedProductInConnector($product_id, $mfsc_id)`
- Този метод намира конектора и извиква `_trackSyncedProduct($product_id)` в конектора
- `product_id` се добавя в масива `$current_sync_product_ids`
- Използва се `array_unique()` за избягване на дублирани записи

### 3. Финализиране в края на синхронизацията

След приключване на обработката на всички продукти се извиква `_finalizeProductTracking($mfsc_id, $current_log, $log_entry_prefix)`:

#### 3.1. Зареждане на предишни синхронизации
```sql
SELECT `product_id` 
FROM `oc_multi_feed_syncer_product_to_connector` 
WHERE `mfsc_id` = '{mfsc_id}'
```

#### 3.2. Намиране на изчезнали продукти
```php
$disappeared_products = array_diff($previous_synced_products, $this->current_sync_product_ids);
```

#### 3.3. Нулиране на бройки
За изчезналите продукти се изпълнява:
```sql
UPDATE `oc_product` 
SET `quantity` = 0, `date_modified` = NOW() 
WHERE `product_id` IN ({product_ids})
```

#### 3.4. Почистване на записи
```sql
DELETE FROM `oc_multi_feed_syncer_product_to_connector` 
WHERE `product_id` IN ({disappeared_product_ids}) 
AND `mfsc_id` = '{mfsc_id}'
```

#### 3.5. Актуализиране на текущи записи
```sql
INSERT INTO `oc_multi_feed_syncer_product_to_connector` 
(`product_id`, `mfsc_id`) 
VALUES {values}
ON DUPLICATE KEY UPDATE `date_modified` = NOW()
```

## Технически детайли

### Оптимизация на производителността
- Използват се batch SQL операции за всички операции
- Минимизират се SQL заявките чрез групиране на операциите
- Използва се `ON DUPLICATE KEY UPDATE` за ефективно актуализиране

### Логиране
Всички операции се логват в:
- Основния лог на синхронизацията
- Специализиран лог файл `mfs_product_tracking.log`

### Обработка на грешки
- Всички операции са обградени с try-catch блокове
- При грешка се записва подробна информация в лога
- Грешките не прекъсват основната синхронизация

## Файлове с промени

### Основен модел (`admin/model/extension/module/multi_feed_syncer.php`)
- Добавена таблица в `install()` метода
- Добавено изтриване на таблица в `uninstall()` метода
- Добавен метод `_trackSyncedProductInConnector()`
- Добавен метод `getConnectorById()`
- Добавено извикване на проследяването в `_insertRelatedProductDataForChunk()` и `_batchUpdateProducts()`

### eOffice конектор (`admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`)
- Добавена променлива `$current_sync_product_ids`
- Добавени методи:
  - `_initializeProductTracking()`
  - `_trackSyncedProduct()` (публичен)
  - `_finalizeProductTracking()`
  - `_zeroQuantityForProducts()`
  - `_removeProductsFromTracking()`
  - `_updateProductTracking()`
- Добавено извикване на инициализация и финализиране в `processSupplierFeed()`

### SQL файл (`sql/multi_feed_syncer_product_to_connector.sql`)
- Създаден SQL файл за ръчно създаване на таблицата

## Тестване

За тестване на функционалността:
1. Стартирайте синхронизация с eOffice конектора
2. Проверете логовете в `mfs_product_tracking.log`
3. Проверете записите в таблицата `multi_feed_syncer_product_to_connector`
4. Симулирайте изчезване на продукти и проверете дали бройките се нулират

## Бъдещи подобрения

- Разширяване на системата за други конектори
- Добавяне на конфигурация за включване/изключване на функционалността
- Добавяне на статистики в интерфейса за броя нулирани продукти
- Възможност за възстановяване на бройки при повторно появяване на продукти
