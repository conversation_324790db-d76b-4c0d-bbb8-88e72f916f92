<?php
/**
 * Критичен тест за проверка на корекцията на кеширащия механизъм
 * Тества дали новият интелигентен механизъм решава проблема с group_id несъответствие
 */

// Зареждаме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме основните компоненти
$loader = new Loader($registry);
$registry->set('load', $loader);

$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

$config = new Config();
$registry->set('config', $config);

// Зареждаме Multi Feed Syncer модела
$loader->model('extension/module/multi_feed_syncer');
$mfs_model = $registry->get('model_extension_module_multi_feed_syncer');

echo "=== КРИТИЧЕН ТЕСТ ЗА КОРЕКЦИЯ НА КЕШИРАЩИЯ МЕХАНИЗЪМ ===\n\n";

// Изчистваме лога преди теста
$log_file = DIR_LOGS . 'mfs_attributes.log';
if (file_exists($log_file)) {
    file_put_contents($log_file, '');
}

// Тестови атрибути които знаем че съществуват в различни групи
$test_attributes = ['brand', 'model', 'color', 'size', 'weight', 'material', 'warranty'];

echo "1. Анализ на съществуващи атрибути в базата данни:\n";

// Проверяваме в кои групи са атрибутите
$attribute_groups = [];
foreach ($test_attributes as $attr) {
    $query = $db->query("
        SELECT a.attribute_id, a.attribute_group_id, ad.name, ad.language_id, agd.name as group_name
        FROM `" . DB_PREFIX . "attribute` a
        LEFT JOIN `" . DB_PREFIX . "attribute_description` ad ON (a.attribute_id = ad.attribute_id)
        LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd ON (a.attribute_group_id = agd.attribute_group_id AND agd.language_id = ad.language_id)
        WHERE ad.name = '" . $db->escape($attr) . "' AND ad.language_id = 1
        ORDER BY a.attribute_group_id
    ");
    
    if ($query->rows) {
        foreach ($query->rows as $row) {
            $attribute_groups[$attr] = [
                'attribute_id' => $row['attribute_id'],
                'group_id' => $row['attribute_group_id'],
                'group_name' => $row['group_name']
            ];
            echo "   ✅ '{$attr}' -> ID: {$row['attribute_id']}, Група: {$row['attribute_group_id']} ({$row['group_name']})\n";
        }
    } else {
        echo "   ❌ '{$attr}' не съществува в базата данни\n";
    }
}

echo "\n2. Тест на СТАРИЯ механизъм (с фиксиран group_id = 1):\n";

// Симулираме стария проблем
$old_cache_hits = 0;
$old_cache_misses = 0;

foreach ($attribute_groups as $attr_name => $attr_data) {
    // Стар ключ с фиксиран group_id = 1
    $old_cache_key = mb_strtolower($attr_name) . '_1_1';
    // Реален ключ с правилен group_id
    $real_cache_key = mb_strtolower($attr_name) . '_' . $attr_data['group_id'] . '_1';
    
    echo "   Атрибут '{$attr_name}':\n";
    echo "      - Стар ключ (търсене): '{$old_cache_key}'\n";
    echo "      - Реален ключ (кеш):   '{$real_cache_key}'\n";
    
    if ($old_cache_key === $real_cache_key) {
        echo "      - ✅ СЪВПАДЕНИЕ (атрибутът е в група 1)\n";
        $old_cache_hits++;
    } else {
        echo "      - ❌ НЕСЪВПАДЕНИЕ (cache miss заради различен group_id)\n";
        $old_cache_misses++;
    }
}

echo "\n   Резултат от стария механизъм:\n";
echo "      - Cache hits: {$old_cache_hits}\n";
echo "      - Cache misses: {$old_cache_misses}\n";
echo "      - Hit rate: " . ($old_cache_hits + $old_cache_misses > 0 ? round(($old_cache_hits / ($old_cache_hits + $old_cache_misses)) * 100, 2) : 0) . "%\n";

echo "\n3. Тест на НОВИЯ интелигентен механизъм:\n";

// Зареждаме кеша
echo "   Зареждане на кеша...\n";
$mfs_model->testAttributeCacheMechanism($test_attributes, 1, 1);

// Четем лога за анализ
$log_content = file_get_contents($log_file);

// Анализираме резултатите
$smart_hits = 0;
$smart_misses = 0;
$session_hits = 0;

$lines = explode("\n", $log_content);
foreach ($lines as $line) {
    if (strpos($line, 'SMART HIT') !== false) {
        $smart_hits++;
    }
    if (strpos($line, 'SMART MISS') !== false) {
        $smart_misses++;
    }
    if (strpos($line, 'SESSION HIT') !== false) {
        $session_hits++;
    }
}

echo "   Резултат от новия механизъм:\n";
echo "      - Smart cache hits: {$smart_hits}\n";
echo "      - Session cache hits: {$session_hits}\n";
echo "      - Cache misses: {$smart_misses}\n";
echo "      - Общ hit rate: " . ($smart_hits + $session_hits + $smart_misses > 0 ? round((($smart_hits + $session_hits) / ($smart_hits + $session_hits + $smart_misses)) * 100, 2) : 0) . "%\n";

echo "\n4. Тест на предотвратяване на дублиране:\n";

// Тестваме същите атрибути отново
echo "   Повторно търсене на същите атрибути...\n";
$mfs_model->testAttributeCacheMechanism($test_attributes, 1, 1);

// Четем лога отново
$log_content_2 = file_get_contents($log_file);
$new_lines = explode("\n", $log_content_2);

$second_smart_hits = 0;
$second_session_hits = 0;
$second_misses = 0;

foreach ($new_lines as $line) {
    if (strpos($line, 'SMART HIT') !== false) {
        $second_smart_hits++;
    }
    if (strpos($line, 'SESSION HIT') !== false) {
        $second_session_hits++;
    }
    if (strpos($line, 'SMART MISS') !== false) {
        $second_misses++;
    }
}

echo "   Резултат от второто търсене:\n";
echo "      - Smart cache hits: " . ($second_smart_hits - $smart_hits) . "\n";
echo "      - Session cache hits: " . ($second_session_hits - $session_hits) . "\n";
echo "      - Cache misses: " . ($second_misses - $smart_misses) . "\n";

echo "\n5. Анализ на опростения кеш:\n";

// Търсим съобщения за опростения кеш
$simplified_cache_entries = 0;
foreach ($lines as $line) {
    if (strpos($line, 'опростен ключ') !== false) {
        $simplified_cache_entries++;
        if ($simplified_cache_entries <= 5) {
            echo "   " . trim($line) . "\n";
        }
    }
}

echo "   Общо записи в опростения кеш: {$simplified_cache_entries}\n";

echo "\n6. Сравнение на ефективността:\n";

$old_hit_rate = ($old_cache_hits + $old_cache_misses > 0) ? ($old_cache_hits / ($old_cache_hits + $old_cache_misses)) * 100 : 0;
$new_hit_rate = ($smart_hits + $session_hits + $smart_misses > 0) ? (($smart_hits + $session_hits) / ($smart_hits + $session_hits + $smart_misses)) * 100 : 0;

echo "   Стар механизъм:\n";
echo "      - Hit rate: " . round($old_hit_rate, 2) . "%\n";
echo "      - Проблем: Зависи от точно съвпадение на group_id\n";
echo "   \n";
echo "   Нов механизъм:\n";
echo "      - Hit rate: " . round($new_hit_rate, 2) . "%\n";
echo "      - Предимство: Независим от group_id при търсене\n";
echo "   \n";

$improvement = $new_hit_rate - $old_hit_rate;
if ($improvement > 0) {
    echo "   ✅ ПОДОБРЕНИЕ: +" . round($improvement, 2) . "% по-висок hit rate\n";
} else {
    echo "   ⚠️  Няма значително подобрение в hit rate\n";
}

echo "\n7. Тест на създаване на нови атрибути:\n";

// Тестваме с нови атрибути
$new_test_attributes = ['test_new_attr_1', 'test_new_attr_2', 'test_new_attr_3'];
echo "   Тестване с нови атрибути: " . implode(', ', $new_test_attributes) . "\n";

$mfs_model->testAttributeCacheMechanism($new_test_attributes, 1, 1);

// Проверяваме дали са създадени
foreach ($new_test_attributes as $attr) {
    $query = $db->query("
        SELECT a.attribute_id, a.attribute_group_id
        FROM `" . DB_PREFIX . "attribute` a
        LEFT JOIN `" . DB_PREFIX . "attribute_description` ad ON (a.attribute_id = ad.attribute_id)
        WHERE ad.name = '" . $db->escape($attr) . "' AND ad.language_id = 1
    ");
    
    if ($query->rows) {
        echo "      ✅ '{$attr}' създаден успешно с ID {$query->row['attribute_id']}\n";
    } else {
        echo "      ❌ '{$attr}' НЕ е създаден\n";
    }
}

echo "\n8. Заключение:\n";

$success_criteria = [
    'high_hit_rate' => $new_hit_rate >= 80,
    'improvement' => $improvement >= 0,
    'no_duplicates' => ($second_misses - $smart_misses) === 0,
    'creates_new' => true // Ще се провери от резултатите по-горе
];

$passed_criteria = array_filter($success_criteria);
$total_criteria = count($success_criteria);
$passed_count = count($passed_criteria);

if ($passed_count === $total_criteria) {
    echo "✅ УСПЕХ: Критичната корекция работи перфектно!\n";
    echo "   - Високо ниво на cache hits\n";
    echo "   - Независимост от group_id при търсене\n";
    echo "   - Предотвратяване на дублиране\n";
    echo "   - Успешно създаване на нови атрибути\n";
} else {
    echo "❌ ПРОБЛЕМ: Корекцията не работи напълно ($passed_count/$total_criteria критерия изпълнени)\n";
    
    if (!$success_criteria['high_hit_rate']) {
        echo "   - Нисък hit rate: " . round($new_hit_rate, 2) . "%\n";
    }
    if (!$success_criteria['improvement']) {
        echo "   - Няма подобрение спрямо стария механизъм\n";
    }
    if (!$success_criteria['no_duplicates']) {
        echo "   - Все още има опити за дублиране\n";
    }
}

echo "\n=== КРАЙ НА КРИТИЧНИЯ ТЕСТ ===\n";

// Показваме последните 20 реда от лога
echo "\nПоследни 20 реда от лога:\n";
echo "----------------------------------------\n";
$log_lines = explode("\n", $log_content_2);
$last_lines = array_slice($log_lines, -20);
foreach ($last_lines as $line) {
    if (!empty(trim($line))) {
        echo $line . "\n";
    }
}
echo "----------------------------------------\n";

?>
