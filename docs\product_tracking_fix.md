# Корекция на системата за проследяване на синхронизирани продукти

## Проблем

Функционалността за автоматично нулиране на бройки на продукти, които липсват в source файла, работеше некоректно поради batch обработката на продукти на порции.

### Детайли на проблема:
1. Синхронизацията се извършва на порции (chunks) в `doSync()` метода
2. Системата за проследяване (`_finalizeProductTracking()`) се извиква в края на цялата синхронизация
3. Но `_initializeProductTracking()` нулираше `self::$current_sync_product_ids` при всяко извикване
4. Това водеше до загуба на акумулираните продукти от предишните порции

## Решение

### 1. Добавен флаг за инициализация
```php
private static $product_tracking_initialized = false;
```

### 2. Модифициран `_initializeProductTracking()`
- Проверява дали вече е инициализирано за тази синхронизация
- Нулира масива само при първото инициализиране
- Маркира като инициализирано с флага

```php
private function _initializeProductTracking($mfsc_id) {
    // Ако вече е инициализирано за тази синхронизация, не правим нищо
    if (self::$product_tracking_initialized) {
        return;
    }
    
    // ... логика за инициализация ...
    
    if (self::$product_tracking_enabled) {
        // Нулираме масива само при първото инициализиране
        self::$current_sync_product_ids = [];
        // ... логиране ...
    }
    
    // Маркираме като инициализирано
    self::$product_tracking_initialized = true;
}
```

### 3. Добавен метод за ресетиране
```php
private function _resetProductTracking() {
    self::$current_sync_product_ids = [];
    self::$product_tracking_enabled = false;
    self::$product_tracking_initialized = false;
}
```

### 4. Подобрено логиране
- Логиране на всеки 100-ти проследен продукт
- Детайлна диагностика при финализиране
- Примерни product_id за проследяване на процеса
- Статистики за дублирани записи

## Логика на работа (коригирана)

### 1. Първо извикване на `doSync()`
- `_initializeProductTracking()` се извиква
- Проверява се поддръжката за конектора
- Нулира се `self::$current_sync_product_ids = []`
- Задава се `self::$product_tracking_initialized = true`

### 2. Обработка на продукти на порции
- При всяка обработка на продукт се извиква `_trackSyncedProduct()`
- Продуктите се акумулират в `self::$current_sync_product_ids`
- Логира се прогреса на всеки 100 продукта

### 3. Финализиране в края
- `_finalizeProductTracking()` получава всички акумулирани продукти
- Сравнява с предишните синхронизации
- Нулира бройките на наистина изчезналите продукти
- `_resetProductTracking()` подготвя за следващата синхронизация

## Тестване

### Очаквани резултати в логовете:

```
MultiFeed Syncer: Инициализирано проследяване на продукти за конектор eoffice (ID: X)
MultiFeed Syncer: Проследени 100 продукта до момента
MultiFeed Syncer: Проследени 200 продукта до момента
...
MultiFeed Syncer: Проследени 1500 уникални продукта в текущата синхронизация.
MultiFeed Syncer: Примерни product_id от текущата синхронизация: 123, 124, 125, ...
MultiFeed Syncer: Намерени 1480 продукта от предишни синхронизации.
MultiFeed Syncer: Примерни product_id от предишни синхронизации: 120, 121, 122, ...
MultiFeed Syncer: Сравнение - Предишни: 1480, Текущи: 1500, Изчезнали: 5
MultiFeed Syncer: Намерени 5 продукта които вече не участват в синхронизацията.
MultiFeed Syncer: Примерни изчезнали product_id: 120, 121, 122, 130, 135
MultiFeed Syncer: Нулирани бройки на 5 продукта. (IDs: 120,121,122,130,135)
MultiFeed Syncer: Ресетирано проследяване на продукти за следващата синхронизация
```

### Проверки:
1. Броят на проследени продукти трябва да съответства на общия брой обработени продукти
2. Изчезналите продукти трябва да са само тези, които наистина липсват в source файла
3. Не трябва да има нулиране на продукти от други порции

## Файлове с промени

### `admin/model/extension/module/multi_feed_syncer.php`
- Добавена променлива `self::$product_tracking_initialized`
- Модифициран `_initializeProductTracking()` с проверка за инициализация
- Добавен `_resetProductTracking()` метод
- Подобрено логиране в `_trackSyncedProduct()` и `_finalizeProductTracking()`
- Добавено извикване на `_resetProductTracking()` в края на `doSync()`

## Резултат

Системата за проследяване на продукти сега работи коректно с batch обработката:
- ✅ Акумулира продукти от всички порции
- ✅ Нулира бройки само на наистина изчезнали продукти
- ✅ Предоставя детайлна диагностика в логовете
- ✅ Подготвя се правилно за следващата синхронизация

Проблемът с неправилното нулиране на бройки поради batch обработката е решен.
