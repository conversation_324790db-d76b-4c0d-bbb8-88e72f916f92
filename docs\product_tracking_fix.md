# Корекция на системата за проследяване на синхронизирани продукти (v2.2)

## Проблем

Функционалността за автоматично нулиране на бройки на продукти, които липсват в source файла, работеше некоректно поради неправилното разбиране на архитектурата.

### Реалният проблем:
1. **eOffice конекторът извиква `doSync()` многократно** - веднъж за всяка порция от 20-50 продукта
2. **Всяко извикване на `doSync()` инициализираше и финализираше проследяването** само за тази порция
3. **Системата сравняваше само текущата порция** с всички продукти от предишни синхронизации
4. **Това водеше до неправилно нулиране** на продукти от другите порции, които не бяха в текущата порция

## Решение (v2.2)

### Преместване на системата от основния модел в конектора

Тъй като `doSync()` се извиква многократно за различни порции, системата за проследяване трябва да работи на ниво конектор, а не на ниво `doSync()`.

### 1. Премахване от основния модел
- **Премахнати** всички статични променливи за проследяване
- **Премахнати** всички методи за проследяване
- **Премахнати** извикванията на инициализация и финализиране в `doSync()`

### 2. Добавяне в eOffice конектора
```php
// Променливи за проследяване
private $current_sync_product_ids = [];
private $product_tracking_enabled = false;

// Методи за проследяване
private function _initializeProductTracking($mfsc_id, &$current_log, $log_entry_prefix)
public function _trackSyncedProduct($product_id)
private function _finalizeProductTracking($mfsc_id, &$current_log, $log_entry_prefix)
private function _zeroQuantityForProducts($product_ids, &$current_log, $log_entry_prefix)
private function _removeProductsFromTracking($product_ids, $mfsc_id, &$current_log, $log_entry_prefix)
private function _updateProductTracking($product_ids, $mfsc_id, &$current_log, $log_entry_prefix)
```

### 3. Интеграция с основния модел
```php
// В основния модел - метод за извикване на проследяването от конектора
private function _trackSyncedProductInConnector($product_id, $mfsc_id) {
    $connector_info = $this->getConnectorById($mfsc_id);
    if ($connector_info && $connector_info['connector_key'] === 'eoffice') {
        $this->load->model('extension/module/multi_feed_syncer_connectors/eoffice');
        $this->model_extension_module_multi_feed_syncer_connectors_eoffice->_trackSyncedProduct($product_id);
    }
}
```

### 4. Извиквания в правилните места
- **Инициализация**: В началото на `processSupplierFeed()` в eOffice конектора
- **Проследяване**: При всяка обработка на продукт в `doSync()` чрез `_trackSyncedProductInConnector()`
- **Финализиране**: В края на `processSupplierFeed()` в eOffice конектора

## Логика на работа (коригирана v2.2)

### 1. Инициализация в конектора
- `processSupplierFeed()` в eOffice конектора се извиква веднъж за цялата синхронизация
- `_initializeProductTracking()` се извиква в началото и нулира `$this->current_sync_product_ids = []`
- Задава се `$this->product_tracking_enabled = true`

### 2. Множествени извиквания на `doSync()`
- eOffice конекторът извиква `doSync()` многократно за порции от 20-50 продукта
- При всяка обработка на продукт в `doSync()` се извиква `_trackSyncedProductInConnector()`
- Този метод извиква `_trackSyncedProduct()` в eOffice конектора
- Продуктите се акумулират в `$this->current_sync_product_ids` от всички порции

### 3. Финализиране в края на цялата синхронизация
- `_finalizeProductTracking()` се извиква в края на `processSupplierFeed()`
- Получава всички акумулирани продукти от всички порции
- Сравнява с предишните синхронизации
- Нулира бройките само на наистина изчезналите продукти

## Тестване

### Очаквани резултати в логовете:

```
MultiFeed Syncer: Инициализирано проследяване на продукти за конектор eoffice (ID: X)
MultiFeed Syncer: Проследени 100 продукта до момента
MultiFeed Syncer: Проследени 200 продукта до момента
...
MultiFeed Syncer: Проследени 1500 уникални продукта в текущата синхронизация.
MultiFeed Syncer: Примерни product_id от текущата синхронизация: 123, 124, 125, ...
MultiFeed Syncer: Намерени 1480 продукта от предишни синхронизации.
MultiFeed Syncer: Примерни product_id от предишни синхронизации: 120, 121, 122, ...
MultiFeed Syncer: Сравнение - Предишни: 1480, Текущи: 1500, Изчезнали: 5
MultiFeed Syncer: Намерени 5 продукта които вече не участват в синхронизацията.
MultiFeed Syncer: Примерни изчезнали product_id: 120, 121, 122, 130, 135
MultiFeed Syncer: Нулирани бройки на 5 продукта. (IDs: 120,121,122,130,135)
MultiFeed Syncer: Ресетирано проследяване на продукти за следващата синхронизация
```

### Проверки:
1. Броят на проследени продукти трябва да съответства на общия брой обработени продукти
2. Изчезналите продукти трябва да са само тези, които наистина липсват в source файла
3. Не трябва да има нулиране на продукти от други порции

## Файлове с промени

### `admin/model/extension/module/multi_feed_syncer.php`
- Добавена променлива `self::$product_tracking_initialized`
- Модифициран `_initializeProductTracking()` с проверка за инициализация
- Добавен `_resetProductTracking()` метод
- Подобрено логиране в `_trackSyncedProduct()` и `_finalizeProductTracking()`
- Добавено извикване на `_resetProductTracking()` в края на `doSync()`

## Резултат

Системата за проследяване на продукти сега работи коректно с batch обработката:
- ✅ Акумулира продукти от всички порции
- ✅ Нулира бройки само на наистина изчезнали продукти
- ✅ Предоставя детайлна диагностика в логовете
- ✅ Подготвя се правилно за следващата синхронизация

Проблемът с неправилното нулиране на бройки поради batch обработката е решен.

## Файлове с промени (v2.2)

### `admin/model/extension/module/multi_feed_syncer.php`
- **ПРЕМАХНАТИ** всички статични променливи за проследяване
- **ПРЕМАХНАТИ** всички методи за проследяване (преместени в eOffice конектора)
- **ПРЕМАХНАТИ** автоматичните извиквания в `doSync()`
- **ДОБАВЕН** метод `_trackSyncedProductInConnector($product_id, $mfsc_id)` за извикване на проследяването от конектора
- **ДОБАВЕНИ** извиквания на `_trackSyncedProductInConnector()` в:
  - `_insertRelatedProductDataForChunk()` - за нови продукти
  - `_batchUpdateProducts()` - за съществуващи продукти

### eOffice конектор (`admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`)
- **ДОБАВЕНИ** променливи за проследяване:
  - `$current_sync_product_ids` - масив за акумулиране на продукти
  - `$product_tracking_enabled` - флаг за активиране
- **ДОБАВЕНИ** методи за проследяване:
  - `_initializeProductTracking($mfsc_id, &$current_log, $log_entry_prefix)` - инициализация
  - `_trackSyncedProduct($product_id)` - публичен метод за добавяне на продукт
  - `_finalizeProductTracking($mfsc_id, &$current_log, $log_entry_prefix)` - финализиране
  - `_zeroQuantityForProducts($product_ids, &$current_log, $log_entry_prefix)` - нулиране на бройки
  - `_removeProductsFromTracking($product_ids, $mfsc_id, &$current_log, $log_entry_prefix)` - премахване от проследяване
  - `_updateProductTracking($product_ids, $mfsc_id, &$current_log, $log_entry_prefix)` - актуализиране на проследяване
- **ДОБАВЕНИ** извиквания в `processSupplierFeed()`:
  - `_initializeProductTracking()` в началото
  - `_finalizeProductTracking()` в края

## Архитектурни предимства (v2.2)

### ✅ Правилна логика
- Системата работи на ниво конектор, не на ниво `doSync()`
- Акумулира продукти от всички batch порции
- Финализира само веднъж в края на цялата синхронизация

### ✅ Специфичност за конектора
- Всеки конектор може да има своя логика за проследяване
- eOffice конекторът има пълен контрол над процеса
- Лесно добавяне на поддръжка за други конектори

### ✅ Ясна отговорност
- Основният модел се фокусира на batch обработката
- Конекторът управлява проследяването на продукти
- Няма смесване на отговорности

### ✅ Производителност
- Минимално въздействие върху основния модел
- Ефективно акумулиране на продукти
- Оптимизирани batch операции за базата данни
