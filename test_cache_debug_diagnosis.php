<?php
/**
 * Диагностичен тест за проблема с кеширащия механизъм за атрибути
 * Този файл анализира защо атрибути като 'brand' не се намират в кеша
 */

// Зареждаме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме основните компоненти
$loader = new Loader($registry);
$registry->set('load', $loader);

$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

$config = new Config();
$registry->set('config', $config);

// Зареждаме Multi Feed Syncer модела
$loader->model('extension/module/multi_feed_syncer');
$mfs_model = $registry->get('model_extension_module_multi_feed_syncer');

echo "=== ДИАГНОСТИЧЕН ТЕСТ ЗА КЕШИРАЩИЯ МЕХАНИЗЪМ ===\n\n";

// Изчистваме лога преди теста
$log_file = DIR_LOGS . 'mfs_attributes.log';
if (file_exists($log_file)) {
    file_put_contents($log_file, '');
}

// Тестови атрибути които знаем че съществуват
$test_attributes = ['brand', 'model', 'color', 'size', 'weight'];

echo "1. Проверяваме дали атрибутите съществуват в базата данни:\n";

// Директна проверка в базата данни
foreach ($test_attributes as $attr) {
    $query = $db->query("
        SELECT a.attribute_id, a.attribute_group_id, ad.name, ad.language_id
        FROM `" . DB_PREFIX . "attribute` a
        LEFT JOIN `" . DB_PREFIX . "attribute_description` ad ON (a.attribute_id = ad.attribute_id)
        WHERE ad.name = '" . $db->escape($attr) . "'
        ORDER BY ad.language_id, a.attribute_group_id
    ");
    
    if ($query->rows) {
        echo "   ✅ '{$attr}' съществува в базата данни:\n";
        foreach ($query->rows as $row) {
            echo "      - ID: {$row['attribute_id']}, Group: {$row['attribute_group_id']}, Lang: {$row['language_id']}\n";
        }
    } else {
        echo "   ❌ '{$attr}' НЕ съществува в базата данни\n";
    }
}

echo "\n2. Зареждаме кеша и анализираме съдържанието:\n";

// Зареждаме кеша
$mfs_model->testAttributeCacheMechanism($test_attributes, 1, 1);

// Четем лога за анализ
$log_content = file_get_contents($log_file);
$lines = explode("\n", $log_content);

echo "\n3. Анализ на заредените ключове в кеша:\n";

// Търсим debug съобщения за заредени атрибути
$loaded_attributes = [];
foreach ($lines as $line) {
    if (strpos($line, 'PRELOAD DEBUG SAMPLE') !== false) {
        echo "   Намерено debug съобщение за sample атрибути\n";
    }
    
    if (preg_match("/- '([^']+)' -> '([^']+)' \(ID: (\d+)\)/", $line, $matches)) {
        $loaded_attributes[] = [
            'original_name' => $matches[1],
            'cache_key' => $matches[2],
            'attribute_id' => $matches[3]
        ];
    }
}

if (!empty($loaded_attributes)) {
    echo "   Първите 10 заредени атрибута:\n";
    foreach (array_slice($loaded_attributes, 0, 10) as $attr) {
        echo "      - '{$attr['original_name']}' -> '{$attr['cache_key']}' (ID: {$attr['attribute_id']})\n";
    }
} else {
    echo "   ❌ Не са намерени debug съобщения за заредени атрибути\n";
}

echo "\n4. Анализ на търсенията в кеша:\n";

// Търсим съобщения за търсене в кеша
$search_attempts = [];
foreach ($lines as $line) {
    if (preg_match("/Търсене на атрибут '([^']+)'.*с ключ '([^']+)'/", $line, $matches)) {
        $search_attempts[] = [
            'attribute_name' => $matches[1],
            'search_key' => $matches[2],
            'found' => strpos($line, 'HIT') !== false
        ];
    }
}

foreach ($search_attempts as $search) {
    $status = $search['found'] ? '✅ НАМЕРЕН' : '❌ НЕ НАМЕРЕН';
    echo "   {$status}: '{$search['attribute_name']}' с ключ '{$search['search_key']}'\n";
}

echo "\n5. Анализ на cache miss debug съобщения:\n";

// Търсим debug съобщения за cache miss
$cache_miss_debug = [];
foreach ($lines as $line) {
    if (strpos($line, 'CACHE MISS DEBUG') !== false) {
        echo "   " . trim($line) . "\n";
    }
}

echo "\n6. Анализ на статистиката по групи:\n";

// Търсим статистика по групи
foreach ($lines as $line) {
    if (strpos($line, 'Група ') !== false && strpos($line, 'атрибута') !== false) {
        echo "   " . trim($line) . "\n";
    }
}

echo "\n7. Тест на различни group_id стойности:\n";

// Тестваме с различни group_id
$test_group_ids = [1, 2, 3, 4, 5];
foreach ($test_group_ids as $group_id) {
    echo "   Тестване с group_id = {$group_id}:\n";
    
    $test_result = $mfs_model->testAttributeCacheMechanism(['brand'], $group_id, 1);
    
    if ($test_result['cache_hits'] > 0) {
        echo "      ✅ 'brand' намерен с group_id {$group_id}\n";
    } else {
        echo "      ❌ 'brand' НЕ е намерен с group_id {$group_id}\n";
    }
}

echo "\n8. Анализ на fallback търсенето:\n";

// Търсим съобщения за fallback търсене
foreach ($lines as $line) {
    if (strpos($line, 'FALLBACK') !== false) {
        echo "   " . trim($line) . "\n";
    }
}

echo "\n9. Препоръки за решение на проблема:\n";

// Анализираме резултатите и даваме препоръки
$cache_hits = 0;
$cache_misses = 0;
$fallback_hits = 0;

foreach ($lines as $line) {
    if (strpos($line, 'CACHE HIT') !== false) $cache_hits++;
    if (strpos($line, 'CACHE MISS') !== false) $cache_misses++;
    if (strpos($line, 'FALLBACK SUCCESS') !== false) $fallback_hits++;
}

echo "   Статистика:\n";
echo "      - Cache hits: {$cache_hits}\n";
echo "      - Cache misses: {$cache_misses}\n";
echo "      - Fallback hits: {$fallback_hits}\n";

if ($cache_misses > $cache_hits) {
    echo "\n   🔧 ПРЕПОРЪКИ:\n";
    echo "      1. Проверете дали се използва правилният group_id при търсене\n";
    echo "      2. Проверете дали атрибутите се зареждат с правилните group_id\n";
    echo "      3. Разгледайте възможността за използване само на нормализираното име като ключ\n";
    echo "      4. Активирайте fallback търсенето за по-голяма гъвкавост\n";
} else {
    echo "\n   ✅ Кеширащият механизъм работи правилно!\n";
}

echo "\n10. Предложение за опростяване на ключовете:\n";

echo "   Текущ формат: {normalized_name}_{group_id}_{language_id}\n";
echo "   Предложен формат: {normalized_name}_{language_id}\n";
echo "   \n";
echo "   Предимства на опростения формат:\n";
echo "      - По-малко зависимости от group_id\n";
echo "      - По-лесно намиране на атрибути\n";
echo "      - По-малко вероятност за грешки\n";
echo "   \n";
echo "   Недостатъци:\n";
echo "      - Възможни конфликти при атрибути със същото име в различни групи\n";
echo "      - По-малка гъвкавост при сложни сценарии\n";

echo "\n=== КРАЙ НА ДИАГНОСТИЧНИЯ ТЕСТ ===\n";

// Показваме последните 30 реда от лога
echo "\nПоследни 30 реда от лога:\n";
echo "----------------------------------------\n";
$log_lines = explode("\n", $log_content);
$last_lines = array_slice($log_lines, -30);
foreach ($last_lines as $line) {
    if (!empty(trim($line))) {
        echo $line . "\n";
    }
}
echo "----------------------------------------\n";

?>
