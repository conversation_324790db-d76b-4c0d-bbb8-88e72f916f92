<?php

// Скрипт за повторно опитване на failed изображения
// Примерна cron команда: /usr/bin/php /път/до/вашия/opencart/system/storage/cron/retry_failed_images.php >> /път/до/вашия/opencart/system/storage/logs/retry_failed_images.log 2>&1

// Задаване на по-дълго време за изпълнение
set_time_limit(0);
ini_set('memory_limit', '256M');

require_once(__DIR__ . '/init.php');

// --- Проверка дали скриптът е извикан от CLI ---
if (php_sapi_name() !== 'cli') {
    die("Този скрипт може да бъде стартиран само от командния ред (CLI).");
}

echo "[" . date("Y-m-d H:i:s") . "] Започва повторно опитване на failed изображения...\n";

// --- Извикване на метода на контролера ---
try {
    $registry->get('load')->controller('extension/module/multi_feed_syncer/retryFailedImages');
    echo "[" . date("Y-m-d H:i:s") . "] Повторното опитване на failed изображения завърши успешно.\n";
} catch (\Exception $e) {
    echo "[" . date("Y-m-d H:i:s") . "] Exception: " . $e->getMessage() . "\n";
}
?>
