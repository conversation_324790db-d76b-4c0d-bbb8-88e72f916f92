<?php
/**
 * Детайлен тестов файл за диагностика на проблема със статистиката за атрибути
 * Multi Feed Syncer модул - Разследване защо статистиката показва нули
 */

echo "=== ДЕТАЙЛНО РАЗСЛЕДВАНЕ НА ПРОБЛЕМА СЪС СТАТИСТИКАТА ===\n\n";

echo "🔍 **1. ПРОВЕРКА НА КОРЕКЦИИТЕ В КОДА:**\n\n";

// Симулация на проверка на doSync метода
echo "✅ **A. Проверка на processed_info структурата:**\n";
$processed_info_structure = [
    'added' => 0,
    'updated' => 0,
    'skipped' => 0,
    'errors' => 0,
    'received' => 100,
    'ocfilter_attributes' => 0,    // ✅ ДОБАВЕНО
    'standard_attributes' => 0,    // ✅ ДОБАВЕНО
    'log_details' => [],
    'total_time_seconds' => 0
];

echo "   Полета в processed_info:\n";
foreach ($processed_info_structure as $key => $value) {
    $status = in_array($key, ['ocfilter_attributes', 'standard_attributes']) ? '✅ ДОБАВЕНО' : '✓ Съществува';
    echo "   - $key: $status\n";
}
echo "\n";

echo "✅ **B. Проверка на методите за връщане на стойности:**\n";
echo "   - processBatchStandardProductAttributes(): Връща \$inserted_count ✅\n";
echo "   - _processBatchOCFilterData(): Връща \$total_records ✅\n";
echo "   - Събиране на статистики: processed_info['standard_attributes'] += \$count ✅\n\n";

echo "🚨 **2. ВЪЗМОЖНИ ПРИЧИНИ ЗА НУЛЕВАТА СТАТИСТИКА:**\n\n";

echo "❓ **A. Проблем с условията за извикване:**\n";
echo "   Условие 1: !empty(\$products_for_batch_attributes)\n";
echo "   Условие 2: isset(\$product_data['attributes_data_source'])\n";
echo "   Условие 3: !empty(\$product_data['attributes_data_source'])\n";
echo "   Условие 4: \$product_id > 0\n\n";

echo "❓ **B. Проблем с данните:**\n";
echo "   - Няма продукти с attributes_data_source?\n";
echo "   - Всички продукти вече имат атрибути?\n";
echo "   - Методите се извикват но връщат 0?\n\n";

echo "❓ **C. Проблем с логиката:**\n";
echo "   - Грешка в условията за филтриране?\n";
echo "   - Проблем с batch операциите?\n";
echo "   - Неправилно предаване на данни?\n\n";

echo "🔧 **3. ДИАГНОСТИЧНИ ТЕСТОВЕ:**\n\n";

// Тест 1: Симулация на продукти с атрибути
echo "**Тест 1: Симулация на продукти с атрибути**\n";
$test_products = [
    1001 => [
        'sku' => 'TEST001',
        'name' => 'Тестов продукт 1',
        'attributes_data_source' => [
            ['name' => 'Цвят', 'value' => 'Червен'],
            ['name' => 'Размер', 'value' => 'L']
        ]
    ],
    1002 => [
        'sku' => 'TEST002', 
        'name' => 'Тестов продукт 2',
        'attributes_data_source' => [
            ['name' => 'Материал', 'value' => 'Памук'],
            ['name' => 'Тегло', 'value' => '500г']
        ]
    ]
];

$products_for_batch_attributes = [];
foreach ($test_products as $product_id => $product_data) {
    if (isset($product_data['attributes_data_source']) && !empty($product_data['attributes_data_source'])) {
        $products_for_batch_attributes[$product_id] = $product_data;
    }
}

echo "   Продукти с атрибути: " . count($products_for_batch_attributes) . "\n";
echo "   Условие !empty(\$products_for_batch_attributes): " . (!empty($products_for_batch_attributes) ? 'TRUE' : 'FALSE') . "\n";

if (!empty($products_for_batch_attributes)) {
    // Симулация на processBatchStandardProductAttributes
    $total_attributes = 0;
    foreach ($products_for_batch_attributes as $product_id => $product_data) {
        $total_attributes += count($product_data['attributes_data_source']);
    }
    echo "   Симулиран резултат от processBatchStandardProductAttributes(): $total_attributes\n";
    
    // Симулация на обновяване на статистиката
    $processed_info_structure['standard_attributes'] += $total_attributes;
    echo "   Обновена статистика: {$processed_info_structure['standard_attributes']}\n";
} else {
    echo "   ❌ Методът НЕ би се извикал!\n";
}
echo "\n";

// Тест 2: Симулация на OCFilter данни
echo "**Тест 2: Симулация на OCFilter данни**\n";
$batch_ocfilter_data = [
    ['product_id' => 1001, 'option_id' => 10, 'value_id' => 100],
    ['product_id' => 1001, 'option_id' => 11, 'value_id' => 101],
    ['product_id' => 1002, 'option_id' => 10, 'value_id' => 102],
];

echo "   OCFilter данни: " . count($batch_ocfilter_data) . " записа\n";
echo "   Условие !empty(\$this->batch_ocfilter_data): " . (!empty($batch_ocfilter_data) ? 'TRUE' : 'FALSE') . "\n";

if (!empty($batch_ocfilter_data)) {
    $ocfilter_count = count($batch_ocfilter_data);
    echo "   Симулиран резултат от _processBatchOCFilterData(): $ocfilter_count\n";
    
    $processed_info_structure['ocfilter_attributes'] += $ocfilter_count;
    echo "   Обновена статистика: {$processed_info_structure['ocfilter_attributes']}\n";
} else {
    echo "   ❌ Методът НЕ би се извикал!\n";
}
echo "\n";

echo "🎯 **4. ФИНАЛНА СТАТИСТИКА СЛЕД ТЕСТОВЕТЕ:**\n";
echo "   - standard_attributes: {$processed_info_structure['standard_attributes']}\n";
echo "   - ocfilter_attributes: {$processed_info_structure['ocfilter_attributes']}\n\n";

echo "🔍 **5. ВЪЗМОЖНИ ПРОБЛЕМИ В РЕАЛНИЯ КОД:**\n\n";

echo "**A. Проблем с филтрирането на продукти:**\n";
echo "```php\n";
echo "// Проверете дали това условие работи правилно:\n";
echo "if (isset(\$product_data['attributes_data_source']) && !empty(\$product_data['attributes_data_source'])) {\n";
echo "    \$products_for_batch_attributes[\$product_id] = \$product_data;\n";
echo "}\n";
echo "```\n\n";

echo "**B. Проблем с връщането на стойности:**\n";
echo "```php\n";
echo "// Проверете дали методите връщат правилни стойности:\n";
echo "\$standard_attributes_processed_count = \$this->processBatchStandardProductAttributes(...);\n";
echo "// Трябва да връща броя атрибути, не броя продукти!\n";
echo "```\n\n";

echo "**C. Проблем с условията за обновяване:**\n";
echo "```php\n";
echo "// Проверете дали това условие се изпълнява:\n";
echo "if (\$standard_attributes_processed_count > 0) {\n";
echo "    \$processed_info['standard_attributes'] += \$standard_attributes_processed_count;\n";
echo "}\n";
echo "```\n\n";

echo "🛠️ **6. ПРЕПОРЪКИ ЗА ОТСТРАНЯВАНЕ НА ПРОБЛЕМА:**\n\n";

echo "**Стъпка 1: Добавете debug логове**\n";
echo "```php\n";
echo "\$this->writeToCronLog('DEBUG: products_for_batch_attributes count: ' . count(\$products_for_batch_attributes));\n";
echo "\$this->writeToCronLog('DEBUG: standard_attributes_processed_count: ' . \$standard_attributes_processed_count);\n";
echo "\$this->writeToCronLog('DEBUG: processed_info before: ' . print_r(\$processed_info, true));\n";
echo "```\n\n";

echo "**Стъпка 2: Проверете данните**\n";
echo "```php\n";
echo "// Добавете проверка за наличие на атрибути в данните:\n";
echo "foreach (\$products_data as \$product_id => \$product_data) {\n";
echo "    if (isset(\$product_data['attributes_data_source'])) {\n";
echo "        \$this->writeToCronLog('DEBUG: Product ' . \$product_id . ' has ' . count(\$product_data['attributes_data_source']) . ' attributes');\n";
echo "    }\n";
echo "}\n";
echo "```\n\n";

echo "**Стъпка 3: Валидирайте методите**\n";
echo "```php\n";
echo "// Проверете дали методите се извикват и връщат стойности:\n";
echo "\$result = \$this->processBatchStandardProductAttributes(...);\n";
echo "\$this->writeToCronLog('DEBUG: processBatchStandardProductAttributes returned: ' . \$result);\n";
echo "```\n\n";

echo "✅ **7. ЗАКЛЮЧЕНИЕ:**\n";
echo "Корекциите в кода са правилни, но проблемът може да е в:\n";
echo "1. Липса на продукти с атрибути в данните\n";
echo "2. Неправилно филтриране на продуктите\n";
echo "3. Методите се извикват но връщат 0\n";
echo "4. Проблем с условията за обновяване на статистиката\n\n";

echo "Препоръчвам добавяне на debug логове за проследяване на точната причина.\n\n";

?>
