# Debug ръководство за системата за проследяване на продукти

## Проблем

Въпреки корекциите със статичните променливи, системата за проследяване на продукти все още показва 0 проследени продукта въпреки че синхронизацията обработва хиляди продукти успешно.

## Добавено debug логиране

### 1. В основния модел (`multi_feed_syncer.php`)

#### `doSync()` метод:
```php
$this->writeToCronLog("DEBUG: doSync започна с " . count($opencart_product_data) . " продукта за mfsc_id: {$mfsc_id}", 'mfs_product_tracking.log');
```

#### `_insertRelatedProductDataForChunk()` метод:
```php
$this->writeToCronLog("DEBUG: Извикване на _trackSyncedProductInConnector от _insertRelatedProductDataForChunk за new_product_id: {$new_product_id}", 'mfs_product_tracking.log');
```

#### `_batchUpdateProducts()` метод:
```php
$this->writeToCronLog("DEBUG: Извикване на _trackSyncedProductInConnector от _batchUpdateProducts за product_id: {$product_id}", 'mfs_product_tracking.log');
```

#### `_trackSyncedProductInConnector()` метод:
```php
$this->writeToCronLog("DEBUG: _trackSyncedProductInConnector извикан за product_id: {$product_id}, mfsc_id: {$mfsc_id}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Не е намерена информация за конектор с ID: {$mfsc_id}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Конектор ключ: {$connector_key}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Опит за зареждане на модел: {$connector_model_name}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Модел е зареден успешно", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Извикване на _trackSyncedProduct за product_id: {$product_id}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: _trackSyncedProduct завърши за product_id: {$product_id}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Методът _trackSyncedProduct не е достъпен в конектора", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Модел не е зареден: {$connector_model_name}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Конекторът {$connector_key} не поддържа проследяване", 'mfs_product_tracking.log');
```

### 2. В eOffice конектора (`eoffice.php`)

#### `_initializeProductTracking()` метод:
```php
$this->writeToCronLog("DEBUG: След инициализация - product_tracking_enabled: " . (self::$product_tracking_enabled ? 'true' : 'false'), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: След инициализация - брой продукти: " . count(self::$current_sync_product_ids), 'mfs_product_tracking.log');
```

#### `_trackSyncedProduct()` метод:
```php
$this->writeToCronLog("DEBUG: _trackSyncedProduct извикан за product_id: {$product_id}", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: product_tracking_enabled: " . (self::$product_tracking_enabled ? 'true' : 'false'), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Текущ брой проследени продукти: " . count(self::$current_sync_product_ids), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Продуктът вече е в списъка: " . (in_array($product_id, self::$current_sync_product_ids) ? 'true' : 'false'), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Добавен product_id {$product_id} към проследяването. Нов брой: " . count(self::$current_sync_product_ids), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Проследяването не е активирано", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Product_id {$product_id} вече е в списъка", 'mfs_product_tracking.log');
```

#### `_finalizeProductTracking()` метод:
```php
$this->writeToCronLog("DEBUG: _finalizeProductTracking започна", 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: При финализация - product_tracking_enabled: " . (self::$product_tracking_enabled ? 'true' : 'false'), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: При финализация - брой продукти: " . count(self::$current_sync_product_ids), 'mfs_product_tracking.log');
$this->writeToCronLog("DEBUG: Проследяването не е активирано, излизаме от _finalizeProductTracking", 'mfs_product_tracking.log');
```

## Очаквани резултати в логовете

### Ако всичко работи правилно:
```
DEBUG: doSync започна с 50 продукта за mfsc_id: 1
DEBUG: Извикване на _trackSyncedProductInConnector от _insertRelatedProductDataForChunk за new_product_id: 12345
DEBUG: _trackSyncedProductInConnector извикан за product_id: 12345, mfsc_id: 1
DEBUG: Конектор ключ: eoffice
DEBUG: Опит за зареждане на модел: model_extension_module_multi_feed_syncer_connectors_eoffice
DEBUG: Модел е зареден успешно
DEBUG: Извикване на _trackSyncedProduct за product_id: 12345
DEBUG: _trackSyncedProduct извикан за product_id: 12345
DEBUG: product_tracking_enabled: true
DEBUG: Текущ брой проследени продукти: 0
DEBUG: Продуктът вече е в списъка: false
DEBUG: Добавен product_id 12345 към проследяването. Нов брой: 1
DEBUG: _trackSyncedProduct завърши за product_id: 12345
...
DEBUG: _finalizeProductTracking започна
DEBUG: При финализация - product_tracking_enabled: true
DEBUG: При финализация - брой продукти: 7543
```

### Възможни проблеми и техните симптоми:

#### 1. `doSync()` не се извиква:
```
# Няма записи от типа:
DEBUG: doSync започна с X продукта за mfsc_id: 1
```

#### 2. `_trackSyncedProductInConnector()` не се извиква:
```
# Има записи от doSync, но няма:
DEBUG: Извикване на _trackSyncedProductInConnector от _insertRelatedProductDataForChunk
DEBUG: Извикване на _trackSyncedProductInConnector от _batchUpdateProducts
```

#### 3. Конекторът не се зарежда правилно:
```
DEBUG: _trackSyncedProductInConnector извикан за product_id: 12345, mfsc_id: 1
DEBUG: Не е намерена информация за конектор с ID: 1
# ИЛИ
DEBUG: Конектор ключ: eoffice
DEBUG: Опит за зареждане на модел: model_extension_module_multi_feed_syncer_connectors_eoffice
DEBUG: Модел не е зареден: model_extension_module_multi_feed_syncer_connectors_eoffice
```

#### 4. Методът не е достъпен:
```
DEBUG: Модел е зареден успешно
DEBUG: Методът _trackSyncedProduct не е достъпен в конектора
```

#### 5. Проследяването не е активирано:
```
DEBUG: _trackSyncedProduct извикан за product_id: 12345
DEBUG: product_tracking_enabled: false
DEBUG: Проследяването не е активирано
```

#### 6. Статичните променливи не се споделят:
```
DEBUG: След инициализация - product_tracking_enabled: true
DEBUG: След инициализация - брой продукти: 0
...
DEBUG: _trackSyncedProduct извикан за product_id: 12345
DEBUG: product_tracking_enabled: false  # <- Проблем!
```

## Стъпки за диагностика

1. **Стартирай синхронизация** и провери `mfs_product_tracking.log`
2. **Провери дали `doSync()` се извиква** - търси "DEBUG: doSync започна"
3. **Провери дали се извикват tracking методите** - търси "DEBUG: Извикване на _trackSyncedProductInConnector"
4. **Провери зареждането на конектора** - търси "DEBUG: Модел е зареден успешно"
5. **Провери статичните променливи** - сравни стойностите между инициализация и извикванията
6. **Провери финализацията** - търси "DEBUG: При финализация - брой продукти"

## Премахване на debug логирането

След като проблемът е намерен и решен, премахни всички редове започващи с:
```php
$this->writeToCronLog("DEBUG: ...
```

## Файлове за редактиране

- `admin/model/extension/module/multi_feed_syncer.php`
- `admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`

Този debug процес ще ни помогне да идентифицираме точно къде се губи връзката в системата за проследяване на продукти.
