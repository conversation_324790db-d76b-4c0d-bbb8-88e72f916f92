<?php

/**
 * Парсва XML файл и извежда списък на всички главни полета
 * 
 * @param string $xmlFilePath Път към XML файла
 * @return array Масив с уникалните имена на полетата
 */
function getXmlFields($xmlFilePath) {
    if (!file_exists($xmlFilePath)) {
        throw new Exception("XML файлът не съществува: " . $xmlFilePath);
    }

    $xml = simplexml_load_file($xmlFilePath);
    if ($xml === false) {
        throw new Exception("Грешка при зареждане на XML файла");
    }

    $fields = [];
    extractFields($xml, $fields);
    
    return array_unique($fields);
}

/**
 * Рекурсивно извлича имената на полетата от XML структурата
 * 
 * @param SimpleXMLElement $element XML елемент
 * @param array &$fields Референция към масива с полетата
 */
function extractFields($element, &$fields) {
    // Добавяме името на текущия елемент
    $fields[] = $element->getName();
    
    // Рекурсивно обхождаме всички дъщерни елементи
    foreach ($element->children() as $child) {
        extractFields($child, $fields);
    }
}

/**
 * Показва списък с полетата в четим формат
 * 
 * @param string $xmlFilePath Път към XML файла
 */
function displayXmlFields($xmlFilePath) {
    try {
        $fields = getXmlFields($xmlFilePath);
        
        echo "Намерени полета в XML файла:\n";
        echo str_repeat("-", 30) . "\n";
        
        foreach ($fields as $index => $field) {
            echo ($index + 1) . ". " . $field . "\n";
        }
        
        echo "\nОбщо полета: " . count($fields) . "\n";
        
    } catch (Exception $e) {
        echo "Грешка: " . $e->getMessage() . "\n";
    }
}

displayXmlFields('path/to/your/products.xml');

?>
