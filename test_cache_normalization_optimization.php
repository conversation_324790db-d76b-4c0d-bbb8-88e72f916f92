<?php
/**
 * Тест за проверка на оптимизацията на кеширащия механизъм с нормализация на имената
 * Този файл тества дали новият механизъм с lowercase ключове работи правилно
 */

// Зареждаме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме основните компоненти
$loader = new Loader($registry);
$registry->set('load', $loader);

$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

$config = new Config();
$registry->set('config', $config);

// Зареждаме Multi Feed Syncer модела
$loader->model('extension/module/multi_feed_syncer');
$mfs_model = $registry->get('model_extension_module_multi_feed_syncer');

echo "=== ТЕСТ НА ОПТИМИЗАЦИЯТА С НОРМАЛИЗАЦИЯ НА КЕШИРАЩИЯ МЕХАНИЗЪМ ===\n\n";

// Тестови атрибути с различни case вариации
$test_attributes = [
    'Number_Pages',      // Mixed case
    'COLOR_A39',         // Uppercase
    'a118',              // Lowercase
    'Razmeri__Sh__D__V___CM',  // Mixed case с специални символи
    'TEGLO__KG',         // Uppercase
    'Seria',             // Capitalized
    'SOFTUER',           // Uppercase
    'protsesor__seria',  // Lowercase
    'Pamet__GB',         // Mixed case
    'test_New_Attribute_1'  // Mixed case нов атрибут
];

echo "Тестови атрибути с различни case вариации:\n";
foreach ($test_attributes as $i => $attr) {
    echo "   " . ($i + 1) . ". '{$attr}' -> нормализиран: '" . mb_strtolower($attr) . "'\n";
}
echo "\n";

// Изчистваме лога преди теста
$log_file = DIR_LOGS . 'mfs_attributes.log';
if (file_exists($log_file)) {
    file_put_contents($log_file, '');
}

echo "1. Започваме тест на нормализирания кеширащ механизъм...\n";

// Извикваме тестовия метод
$test_result = $mfs_model->testAttributeCacheMechanism($test_attributes, 1, 1);

echo "\n2. Резултати от теста:\n";
echo "   - Общо тествани атрибути: " . $test_result['total_tested'] . "\n";
echo "   - Cache hits: " . $test_result['cache_hits'] . "\n";
echo "   - Cache misses: " . $test_result['cache_misses'] . "\n";
echo "   - Cache hit rate: " . $test_result['cache_hit_rate'] . "%\n";
echo "   - Резултати получени: " . $test_result['result_count'] . "\n";
echo "   - Размер на кеша: " . $test_result['cache_size'] . "\n";
echo "   - Статус на теста: " . $test_result['test_status'] . "\n\n";

echo "3. Проверяваме лога за нормализирани ключове...\n";

// Четем лога и търсим нормализирани ключове
$log_content = file_get_contents($log_file);
$lines = explode("\n", $log_content);

$normalized_keys_found = 0;
$md5_keys_found = 0;
$cache_operations = [];

foreach ($lines as $line) {
    // Търсим нормализирани ключове (lowercase с подчертавки)
    if (preg_match('/ключ \'([a-z0-9_]+_\d+_\d+)\'/', $line, $matches)) {
        $normalized_keys_found++;
        $cache_operations[] = [
            'type' => 'normalized_key',
            'key' => $matches[1],
            'line' => trim($line)
        ];
    }
    
    // Търсим MD5 ключове (32 символа hex)
    if (preg_match('/ключ \'([a-f0-9]{32})\'/', $line, $matches)) {
        $md5_keys_found++;
        $cache_operations[] = [
            'type' => 'md5_key',
            'key' => $matches[1],
            'line' => trim($line)
        ];
    }
}

echo "   - Намерени нормализирани ключове: $normalized_keys_found\n";
echo "   - Намерени MD5 ключове: $md5_keys_found\n";

if ($normalized_keys_found > 0 && $md5_keys_found === 0) {
    echo "\n✅ ОТЛИЧНО: Използват се само нормализирани ключове!\n";
} elseif ($md5_keys_found > 0) {
    echo "\n❌ ПРОБЛЕМ: Все още се използват MD5 ключове!\n";
} else {
    echo "\n⚠️  ВНИМАНИЕ: Не са намерени ключове в лога\n";
}

echo "\n4. Анализ на cache операциите:\n";

// Показваме първите 5 cache операции за debug
$shown_operations = 0;
foreach ($cache_operations as $operation) {
    if ($shown_operations >= 5) break;
    
    echo "   " . ($shown_operations + 1) . ". " . $operation['type'] . ": " . $operation['key'] . "\n";
    echo "      " . substr($operation['line'], 0, 100) . "...\n";
    $shown_operations++;
}

echo "\n5. Тест на case-insensitive търсене:\n";

// Тестваме дали различни case вариации намират същия атрибут
$case_test_pairs = [
    ['number_pages', 'NUMBER_PAGES'],
    ['color_a39', 'Color_A39'],
    ['seria', 'SERIA']
];

// Тестваме чрез публичния тестов метод
foreach ($case_test_pairs as $pair) {
    $lower_result = $mfs_model->testAttributeCacheMechanism([$pair[0]], 1, 1);
    $upper_result = $mfs_model->testAttributeCacheMechanism([$pair[1]], 1, 1);

    if ($lower_result['test_status'] === 'SUCCESS' && $upper_result['test_status'] === 'SUCCESS') {
        echo "   ✅ '{$pair[0]}' и '{$pair[1]}' са обработени успешно с нормализирани ключове\n";
    } else {
        echo "   ❌ Проблем при тестване на '{$pair[0]}' и '{$pair[1]}'\n";
    }
}

echo "\n6. Производителност на новия механизъм:\n";

// Измерваме времето за търсене в кеша
$start_time = microtime(true);
for ($i = 0; $i < 1000; $i++) {
    $test_name = 'test_attribute_' . $i;
    $normalized_name = mb_strtolower($test_name);
    $cache_key = $normalized_name . '_1_1';
    // Симулираме търсене в кеша
    $dummy = isset($test_result) ? true : false;
}
$end_time = microtime(true);
$search_time = round(($end_time - $start_time) * 1000, 3);

echo "   - Време за 1000 нормализирани търсения: {$search_time}ms\n";
echo "   - Средно време за търсене: " . round($search_time / 1000, 6) . "ms\n";

echo "\n7. Заключение:\n";

$success_criteria = [
    'normalized_keys_used' => $normalized_keys_found > 0 && $md5_keys_found === 0,
    'test_passed' => $test_result['test_status'] === 'SUCCESS',
    'good_hit_rate' => $test_result['cache_hit_rate'] >= 70,
    'fast_performance' => $search_time < 10
];

$passed_criteria = array_filter($success_criteria);
$total_criteria = count($success_criteria);
$passed_count = count($passed_criteria);

if ($passed_count === $total_criteria) {
    echo "✅ УСПЕХ: Оптимизацията с нормализация работи перфектно!\n";
    echo "   - Използват се само нормализирани ключове\n";
    echo "   - Case-insensitive търсенето функционира правилно\n";
    echo "   - Производителността е отлична\n";
    echo "   - Всички атрибути са обработени успешно\n";
} else {
    echo "❌ ПРОБЛЕМ: Оптимизацията не работи напълно ($passed_count/$total_criteria критерия изпълнени)\n";
    
    if (!$success_criteria['normalized_keys_used']) {
        echo "   - Все още се използват MD5 ключове\n";
    }
    if (!$success_criteria['test_passed']) {
        echo "   - Тестът не премина успешно\n";
    }
    if (!$success_criteria['good_hit_rate']) {
        echo "   - Ниска cache hit rate: {$test_result['cache_hit_rate']}%\n";
    }
    if (!$success_criteria['fast_performance']) {
        echo "   - Бавна производителност: {$search_time}ms\n";
    }
}

echo "\n=== КРАЙ НА ТЕСТА ===\n";

// Показваме последните 15 реда от лога за debug
echo "\nПоследни 15 реда от лога:\n";
echo "----------------------------------------\n";
$log_lines = explode("\n", $log_content);
$last_lines = array_slice($log_lines, -15);
foreach ($last_lines as $line) {
    if (!empty(trim($line))) {
        echo $line . "\n";
    }
}
echo "----------------------------------------\n";

?>
