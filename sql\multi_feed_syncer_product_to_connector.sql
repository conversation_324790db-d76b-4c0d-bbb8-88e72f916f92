-- SQL код за създаване на таблица за проследяване на синхронизирани продукти
-- Този файл съдържа SQL кода за създаване на таблицата multi_feed_syncer_product_to_connector

CREATE TABLE IF NOT EXISTS `{{prefix}}multi_feed_syncer_product_to_connector` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `mfsc_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_connector` (`product_id`, `mfsc_id`),
  KEY `mfsc_id` (`mfsc_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Описание на полетата:
-- id: Уникален идентификатор на записа
-- product_id: ID на продукта от OpenCart (връзка към oc_product)
-- mfsc_id: ID на конектора (връзка към multi_feed_syncer_connectors)
-- date_added: Дата на първо добавяне на записа
-- date_modified: Дата на последна модификация на записа

-- Индекси:
-- PRIMARY KEY: Основен ключ на id
-- UNIQUE KEY product_connector: Уникална комбинация от product_id и mfsc_id
-- KEY mfsc_id: Индекс за бързо търсене по конектор
-- KEY product_id: Индекс за бързо търсене по продукт

-- Функционалност:
-- Тази таблица се използва за проследяване на кои продукти са синхронизирани от всеки конектор
-- При всяка синхронизация се записват всички обработени продукти
-- В края на синхронизацията се сравняват текущите продукти с предишните
-- Продуктите които вече не участват в синхронизацията получават quantity = 0
-- Записите за изчезналите продукти се изтриват от тази таблица
