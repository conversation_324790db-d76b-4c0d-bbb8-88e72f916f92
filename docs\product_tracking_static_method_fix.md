# Корекция на проблема с model loading в системата за проследяване на продукти

## Идентифициран проблем

От debug логовете се установи, че основният модел (`multi_feed_syncer.php`) не може да зареди eOffice конектора отново въпреки че конекторът вече е зареден и работи:

```
DEBUG: _trackSyncedProductInConnector извикан за product_id: 70932, mfsc_id: 1
DEBUG: Конектор ключ: eoffice
DEBUG: Опит за зареждане на модел: model_extension_module_multi_feed_syncer_connectors_eoffice
DEBUG: Модел не е зареден: model_extension_module_multi_feed_syncer_connectors_eoffice
```

### Причина:
OpenCart model loading системата има ограничения или конфликти когато се опитваме да заредим същия модел отново от различен контекст.

## Решение: Статичен метод подход

Вместо да се опитваме да заредим конектора отново, използваме статичен метод за директно извикване на проследяването.

### Архитектура на решението:

#### 1. Статична референция към активната instance
```php
private static $active_instance = null; // Референция към активната instance
```

#### 2. Запазване на референцията при инициализация
```php
private function _initializeProductTracking($mfsc_id, &$current_log, $log_entry_prefix) {
    self::$current_sync_product_ids = [];
    self::$product_tracking_enabled = true;
    self::$active_instance = $this; // Запазваме референция към активната instance
    // ...
}
```

#### 3. Статичен метод за външно извикване
```php
public static function trackSyncedProductStatic($product_id) {
    if (self::$active_instance && self::$product_tracking_enabled) {
        self::$active_instance->_trackSyncedProduct($product_id);
        return true;
    }
    return false;
}
```

#### 4. Използване на статичния метод от основния модел
```php
private function _trackSyncedProductInConnector($product_id, $mfsc_id) {
    // ...
    if ($connector_key === 'eoffice') {
        if (class_exists('ModelExtensionModuleMultiFeedSyncerConnectorsEoffice')) {
            $success = ModelExtensionModuleMultiFeedSyncerConnectorsEoffice::trackSyncedProductStatic($product_id);
        }
    }
    // ...
}
```

## Файлове с промени

### `admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`

#### Добавени променливи:
```php
private static $active_instance = null; // Референция към активната instance
```

#### Модифицирани методи:
- **`_initializeProductTracking()`**: Запазва `self::$active_instance = $this`
- **`_resetProductTracking()`**: Изчиства `self::$active_instance = null`

#### Добавени методи:
```php
public static function trackSyncedProductStatic($product_id) {
    if (self::$active_instance && self::$product_tracking_enabled) {
        self::$active_instance->_trackSyncedProduct($product_id);
        return true;
    }
    return false;
}
```

### `admin/model/extension/module/multi_feed_syncer.php`

#### Модифицирани методи:
- **`_trackSyncedProductInConnector()`**: Използва статичния метод вместо model loading

## Логика на работа

### 1. Инициализация (Instance A - Оригинална)
```
processSupplierFeed() → _initializeProductTracking()
↓
self::$active_instance = $this
self::$product_tracking_enabled = true
```

### 2. Проследяване (Instance B - Нова от основния модел)
```
doSync() → _trackSyncedProductInConnector()
↓
ModelExtensionModuleMultiFeedSyncerConnectorsEoffice::trackSyncedProductStatic($product_id)
↓
self::$active_instance->_trackSyncedProduct($product_id)
```

### 3. Финализиране (Instance A - Оригинална)
```
processSupplierFeed() → _finalizeProductTracking()
↓
Използва self::$current_sync_product_ids от статичните променливи
```

## Предимства на новия подход

### ✅ Заобикаля model loading проблемите
- Не се опитваме да заредим същия модел отново
- Използваме директно статичен метод

### ✅ Запазва данните между instances
- Статичните променливи се споделят
- Активната instance е достъпна чрез статична референция

### ✅ Ясна архитектура
- Един статичен entry point за външно извикване
- Вътрешната логика остава непроменена

### ✅ Надеждност
- Проверява дали класът съществува
- Проверява дали активната instance е налична
- Връща статус за успех/неуспех

## Очаквани резултати в логовете

### Успешно извикване:
```
DEBUG: _trackSyncedProductInConnector извикан за product_id: 70932, mfsc_id: 1
DEBUG: Конектор ключ: eoffice
DEBUG: Извикване на статичен метод trackSyncedProductStatic за product_id: 70932
DEBUG: trackSyncedProductStatic извикан за product_id: 70932
DEBUG: _trackSyncedProduct извикан за product_id: 70932
DEBUG: product_tracking_enabled: true
DEBUG: Добавен product_id 70932 към проследяването. Нов брой: 1
DEBUG: trackSyncedProductStatic завърши успешно за product_id: 70932
```

### При проблеми:
```
DEBUG: Класът ModelExtensionModuleMultiFeedSyncerConnectorsEoffice не съществува
# ИЛИ
DEBUG: trackSyncedProductStatic неуспешно за product_id: 70932
DEBUG: trackSyncedProductStatic - проследяването не е активирано
```

## Тестване

1. **Стартирай синхронизация** и провери `mfs_product_tracking.log`
2. **Търси записи** за "trackSyncedProductStatic"
3. **Провери финалния резултат** - трябва да има проследени продукти вместо 0

Този подход решава проблема с model loading и осигурява надеждно проследяване на продукти чрез статичен метод.
