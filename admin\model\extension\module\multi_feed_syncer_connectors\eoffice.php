<?php
// Създаваме копие на файла преди редакция
$backup_filename = __FILE__ . '.backup_' . date('Ymd_His');
if (!file_exists($backup_filename)) {
    copy(__FILE__, $backup_filename);
}

class ModelExtensionModuleMultiFeedSyncerConnectorsEoffice extends Model {
    private $connector_key = 'eoffice'; // Ключ на конектора (латиница)
    private $connector_name = 'Eoffice.bg'; // Име на доставчика
    // URL за извличане на XML данни от Eoffice.bg
    private $xml_url = 'https://eoffice.bg/module.php?ModuleName=com.seliton.superxmlexport&Username=Noxes&Domain=noxes.org&Signature=4f4d3d55284c1036e8c061472d884cde62662952&DealerAccountType=0';

    // Кеш за атрибути от XML
    private $attributes_cache = [];
    private $attributes_cache_loaded = false;

    // Кеш за OpenCart атрибути
    private $opencart_attributes_cache = [];
    private $opencart_attributes_cache_loaded = false;

    // Кеш за марки/производители
    private $manufacturers_cache = [];
    private $manufacturers_cache_loaded = false;
    
    /**
     * Конструктор на класа - задава лимит на паметта
     */
    public function __construct($registry) {
        parent::__construct($registry);
        ini_set('memory_limit', '512M');
    }

    /**
     * Връща данни, необходими за регистриране на този конектор
     * в таблицата `multi_feed_syncer_connectors`.
     * @return array
     */
    public function getInstallData() {
        return [
            'connector'     => $this->connector_name,
            'connector_key' => $this->connector_key,
            'markup_percentage' => 0
        ];
    }

    /**
     * Връща типа на връзката (напр. "XML Link", "API").
     * @return string
     */
    public function getConnectionType() {
        $text_copy_link = 'Копирай';
        $text_copied_link = 'Копирано!';
        $text_copy_error = 'Грешка!';

        $html = 'XML Линк: <span class="text-primary" data-toggle="tooltip" data-placement="top" title="' . htmlspecialchars($this->xml_url) . '">Виж линк</span>';
        $html .= ' <div class="btn-group">';
        $html .= '<button type="button" class="btn btn-default btn-xs btn-copy" data-clipboard-text="' . htmlspecialchars($this->xml_url) . '" title="Копирай XML линка">';
        $html .= '<i class="fa fa-copy"></i> <span class="copy-text">' . $text_copy_link . '</span>';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '<script>';
        $html .= '    $(document).ready(function() {' . "\n";
        $html .= '        $(".btn-copy").off("click.customCopy").on("click.customCopy", function(e) {' . "\n";
        $html .= '            e.preventDefault();' . "\n";
        $html .= '            var $button = $(this);' . "\n";
        $html .= '            var linkText = $button.data("clipboard-text");' . "\n";
        $html .= '            var $copyTextSpan = $button.find(".copy-text");' . "\n";
        $html .= '            var originalText = $copyTextSpan.text();' . "\n";
        $html .= '            var $icon = $button.find("i");' . "\n";
        $html .= '            var originalIconClass = $icon.attr("class");' . "\n";
        $html .= "\n";
        $html .= '            if (linkText && linkText.trim() !== "") {' . "\n";
        $html .= '                navigator.clipboard.writeText(linkText).then(function() {' . "\n";
        $html .= '                    $button.addClass("btn-success");' . "\n";
        $html .= '                    $copyTextSpan.text("' . $text_copied_link . '");' . "\n";
        $html .= '                    $icon.attr("class", "fa fa-check");' . "\n";
        $html .= "\n";
        $html .= '                    var $copiedMsg = $("<span>Копирано</span>").css({' . "\n";
        $html .= '                        "position": "fixed", "top": "20px", "left": "50%", "transform": "translateX(-50%)",' . "\n";
        $html .= '                        "background-color": "#4CAF50", "color": "white", "padding": "10px 20px",' . "\n";
        $html .= '                        "border-radius": "5px", "z-index": "10000", "font-size": "16px", "display": "none"' . "\n";
        $html .= '                    });' . "\n";
        $html .= '                    $("body").append($copiedMsg);' . "\n";
        $html .= '                    $copiedMsg.fadeIn(200).delay(1500).fadeOut(500, function() { $(this).remove(); });' . "\n";
        $html .= "\n";
        $html .= '                    setTimeout(function() {' . "\n";
        $html .= '                        $button.removeClass("btn-success");' . "\n";
        $html .= '                        $copyTextSpan.text(originalText);' . "\n";
        $html .= '                        $icon.attr("class", originalIconClass);' . "\n";
        $html .= '                    }, 2000);' . "\n";
        $html .= '                }).catch(function(err) {' . "\n";
        $html .= '                    console.error("Грешка при копиране: ", err);' . "\n";
        $html .= '                    $copyTextSpan.text("' . $text_copy_error . '");' . "\n";
        $html .= '                    $icon.attr("class", "fa fa-times");' . "\n";
        $html .= "\n";
        $html .= '                    var $errorMsg = $("<span>Грешка при копиране</span>").css({' . "\n";
        $html .= '                        "position": "fixed", "top": "20px", "left": "50%", "transform": "translateX(-50%)",';
        $html .= '                        "background-color": "#f44336", "color": "white", "padding": "10px 20px",';
        $html .= '                        "border-radius": "5px", "z-index": "10000", "font-size": "16px", "display": "none"';
        $html .= '                    });' . "\n";
        $html .= '                    $("body").append($errorMsg);' . "\n";
        $html .= '                    $errorMsg.fadeIn(200).delay(2000).fadeOut(500, function() { $(this).remove(); });' . "\n";
        $html .= "\n";
        $html .= '                    setTimeout(function() {' . "\n";
        $html .= '                        $copyTextSpan.text(originalText);' . "\n";
        $html .= '                        $icon.attr("class", originalIconClass);' . "\n";
        $html .= '                    }, 2500);' . "\n";
        $html .= '                });' . "\n";
        $html .= '            } else {' . "\n";
        $html .= '                console.warn("Няма текст за копиране в data-clipboard-text атрибута на бутона.");' . "\n";
        $html .= '            }' . "\n";
        $html .= '        });' . "\n";
        $html .= '    });' . "\n";
        $html .= '</script>';
        $html .= '<style>.btn-copy { position: relative; transition: all 0.2s ease-in-out; } .btn-copy.btn-success { background-color: #5cb85c !important; border-color: #4cae4c !important; color: #fff !important; } .btn-copy.btn-success .fa-copy { display:none; } .btn-copy .fa-check { display:none; } .btn-copy.btn-success .fa-check { display:inline-block; }</style>';
        return $html;
    }

    /**
     * Връща информация за настройка на cron задача.
     * @return string
     */
    public function getCronInfo() {
        $cron_file_path = DIR_STORAGE . 'cron/multi_feed_syncer.php';
        $command = '/usr/local/bin/php ' . $cron_file_path . ' eoffice >> ' . DIR_LOGS . 'multi_feed_syncer.log 2>&1';
        return $command;
    }

    /**
     * Проверява дали източникът на данни (напр. XML линк) е достъпен.
     * @return bool True ако е достъпен, false в противен случай.
     */
    public function checkConnection() {
        $ch = curl_init($this->xml_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ($http_code >= 200 && $http_code < 300);
    }

    /**
     * Извлича данни от доставчика.
     * @return string Сурови данни (напр. XML низ).
     */
    public function requestSyncData() {
        $ch = curl_init($this->xml_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);
        $xml_data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code == 200 && $xml_data) {
            return $xml_data;
        }
        return false;
    }

    /**
     * Конвертира XML данни в PHP масив.
     * @param string $xml_data
     * @return array
     */
    public function convertXMLdataToArray($xml_data) {
        // Използваме LIBXML_NOCDATA за да запазим CDATA секции
        // и ENT_QUOTES за да запазим кавички
        $xml = simplexml_load_string($xml_data, "SimpleXMLElement", LIBXML_NOCDATA | LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        if ($xml === false) {
            return [];
        }

        // Използваме JSON_UNESCAPED_UNICODE за да запазим специални символи
        $json = json_encode($xml, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $array = json_decode($json, TRUE);

        return $array ? $array : [];
    }

    /**
     * Мапира масива с данни от доставчика към структурата на Opencart продукт.
     * @param array $supplier_product_data Данните за един продукт от доставчика
     * @param array $full_feed_data Пълният фийд данни (за достъп до глобални елементи като OptionGroups)
     * @return array Масив с данни за Opencart продукт.
     */
    public function mapDataToOpencartProduct(array $supplier_product_data, array $full_feed_data = []) {


        // $this->writeToCronLog("MultiFeed Syncer: Mapping product data to Opencart product", 'mfs_debug.log');

        $opencart_product = $this->_initializeOpencartProduct();

        $this->_mapBasicProductData($opencart_product, $supplier_product_data);
        $this->_mapProductImages($opencart_product, $supplier_product_data);
        $this->_mapProductWeight($opencart_product, $supplier_product_data);
        $this->_mapProductStockStatus($opencart_product, $supplier_product_data);
        $this->_mapProductCategories($opencart_product, $supplier_product_data);
        $this->_mapProductManufacturer($opencart_product, $supplier_product_data);
        $this->_mapProductAttributes($opencart_product, $supplier_product_data);
        $this->_mapProductSeoData($opencart_product, $supplier_product_data);
        $this->_mapProductOptions($opencart_product, $supplier_product_data, $full_feed_data);

        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        $processed_attributes = $this->processProductAttributes($opencart_product, $language_id);

        // Заменяме attributes_data_source с обработените атрибути
        $opencart_product['processed_attributes'] = $processed_attributes;

        // Обработваме марката на продукта от атрибутите
        $manufacturer_id = $this->processProductManufacturer($opencart_product);
        if ($manufacturer_id) {
            $opencart_product['manufacturer_id'] = $manufacturer_id;
            if(!$opencart_product['manufacturer'] && $opencart_product['manufacturer_name_source']) {
                $opencart_product['manufacturer'] = $opencart_product['manufacturer_name_source'];
            }
        }

        // Освобождаваме входните данни за спестяване на памет
        unset($supplier_product_data, $full_feed_data);

        return $opencart_product;
    }

    /**
     * Извлича текстова стойност от потенциално вложен масив, върнат от _convertDomNodeToArray.
     *
     * @param mixed $node_data Данните от XML възела.
     * @param string $default Стойност по подразбиране, ако не се намери текстова стойност.
     * @return string Текстовата стойност или стойността по подразбиране.
     */
    private function _getXmlNodeValue($node_data, $default = '') {
        if (is_array($node_data)) {
            if (isset($node_data['#text'])) {
                $value = (string)$node_data['#text'];
                // Декодираме HTML entities за да запазим специални символи като кавички
                return html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
            if (isset($node_data[0]) && is_string($node_data[0])) {
                $value = (string)$node_data[0];
                // Декодираме HTML entities за да запазим специални символи като кавички
                return html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
            return $default;
        }

        $value = (string)$node_data;
        // Декодираме HTML entities за да запазим специални символи като кавички
        return html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    private function _initializeOpencartProduct() {
        $opencart_product = [];
        $oc_fields = [
            'product_id', 'name', 'description', 'meta_title', 'meta_description',
            'meta_keyword', 'tag', 'model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn',
            'location', 'quantity', 'stock_status_id', 'image', 'manufacturer_id',
            'manufacturer', 'price', 'special', 'reward', 'points', 'tax_class_id',
            'date_available', 'weight', 'weight_class_id', 'length', 'width', 'height',
            'length_class_id', 'subtract', 'minimum', 'sort_order', 'status',
            'date_added', 'date_modified', 'viewed',
            'product_category', 'product_attribute', 'product_option', 'product_image',
            'categories_data_source', 'attributes_data_source', 'manufacturer_name_source'
        ];

        foreach ($oc_fields as $field) {
            $opencart_product[$field] = '';
            if (in_array($field, ['product_category', 'product_attribute', 'product_option', 'product_image', 'categories_data_source', 'attributes_data_source'])) {
                $opencart_product[$field] = [];
            }
        }
        return $opencart_product;
    }

    private function _mapBasicProductData(array &$opencart_product, array $supplier_product_data) {
        $opencart_product['model'] = isset($supplier_product_data['ProductCode']) ? $this->_getXmlNodeValue($supplier_product_data['ProductCode']) : '';
        $opencart_product['sku'] = isset($supplier_product_data['ProductCode']) ? $this->_getXmlNodeValue($supplier_product_data['ProductCode']) : '';
        $opencart_product['mpn'] = isset($supplier_product_data['ProductBarcode']) ? $this->_getXmlNodeValue($supplier_product_data['ProductBarcode']) : '';

        if (isset($supplier_product_data['ProductName']['BG'])) {
            $product_name_bg = $this->_getXmlNodeValue($supplier_product_data['ProductName']['BG']);
            // Ограничаваме дължината на името за спестяване на памет
            $product_name_trimmed = mb_strlen($product_name_bg) > 255 ? mb_substr($product_name_bg, 0, 255) . '...' : $product_name_bg;
            $opencart_product['name'] = $product_name_trimmed;
            if (empty($opencart_product['meta_title'])) {
                 $opencart_product['meta_title'] = $product_name_trimmed;
            }
            // Освобождаваме временните променливи
            unset($product_name_bg, $product_name_trimmed);
        }

        if (isset($supplier_product_data['ProductDescription']['BG'])) {
            $description_bg = $this->_getXmlNodeValue($supplier_product_data['ProductDescription']['BG']);
            if (!empty(trim(strip_tags($description_bg)))) {
                // Ограничаваме дължината на описанието за спестяване на памет
                $description_trimmed = mb_strlen($description_bg) > 2000 ? mb_substr($description_bg, 0, 2000) . '...' : $description_bg;
                $opencart_product['description'] = $description_trimmed;
                unset($description_trimmed);
            }
            unset($description_bg);
        } else if (isset($supplier_product_data['ProductDetailedDescription']['BG'])) {
            $detailed_description_bg = $this->_getXmlNodeValue($supplier_product_data['ProductDetailedDescription']['BG']);
            if (!empty(trim(strip_tags($detailed_description_bg)))) {
                // Ограничаваме дължината на детайлното описание за спестяване на памет
                $detailed_desc_trimmed = mb_strlen($detailed_description_bg) > 2000 ? mb_substr($detailed_description_bg, 0, 2000) . '...' : $detailed_description_bg;
                $opencart_product['description'] = $detailed_desc_trimmed;
                unset($detailed_desc_trimmed);
            }
            unset($detailed_description_bg);
        }

        $opencart_product['quantity'] = isset($supplier_product_data['ProductQuantity']) ? (float)$this->_getXmlNodeValue($supplier_product_data['ProductQuantity'], 0) : 0;
        $opencart_product['price'] = isset($supplier_product_data['ProductPrice']) ? (float)$this->_getXmlNodeValue($supplier_product_data['ProductPrice'], 0.00) : 0.00;

        $product_is_active = isset($supplier_product_data['ProductIsActive']) ? $this->_getXmlNodeValue($supplier_product_data['ProductIsActive']) : 'no';
        $opencart_product['status'] = ($product_is_active == 'yes') ? 1 : 0;

        $created_timestamp_str = isset($supplier_product_data['ProductCreatedTimestamp']) ? $this->_getXmlNodeValue($supplier_product_data['ProductCreatedTimestamp']) : '';
        $opencart_product['date_available'] = !empty($created_timestamp_str) ? date('Y-m-d', strtotime($created_timestamp_str)) : date('Y-m-d');
    }

    private function _mapProductImages(array &$opencart_product, array $supplier_product_data) {
        if (isset($supplier_product_data['ProductImages']['ProductImage'])) {
            $images_node = $supplier_product_data['ProductImages']['ProductImage'];

            if (isset($images_node['ImagePath'])) {
                $opencart_product['image'] = $this->_getXmlNodeValue($images_node['ImagePath']);
            } elseif (is_array($images_node) && !empty($images_node)) {
                if (isset($images_node[0]['ImagePath'])) {
                    $main_image_path = $this->_getXmlNodeValue($images_node[0]['ImagePath']);
                    if (!empty($main_image_path)) {
                        $opencart_product['image'] = $main_image_path;
                    }
                }

                // Ограничаваме броя изображения за предотвратяване на memory exhaustion
                $max_images = 20; // Максимум 20 изображения на продукт
                $images_count = 0;
                $sort_order = 0;

                foreach ($images_node as $img_data) {
                    // Проверяваме лимита на изображенията
                    if ($images_count >= $max_images) {
                        break;
                    }

                    if (isset($img_data['ImagePath'])) {
                        $current_image_path = $this->_getXmlNodeValue($img_data['ImagePath']);
                        if (!empty($current_image_path)) {
                            if (empty($opencart_product['image'])) {
                                $opencart_product['image'] = $current_image_path;
                            }
                            $opencart_product['product_image'][] = [
                                'image'      => $current_image_path,
                                'sort_order' => $sort_order++
                            ];
                            $images_count++;
                        }

                        // Освобождаваме временните променливи
                        unset($current_image_path);
                    }
                }

                // Освобождаваме масива с изображения
                unset($images_node);
            }
        }
    }

    private function _mapProductWeight(array &$opencart_product, array $supplier_product_data) {
        $opencart_product['weight'] = isset($supplier_product_data['ProductWeight']) ? (float)$this->_getXmlNodeValue($supplier_product_data['ProductWeight'], 0.00) : 0.00;
    }

    private function _mapProductStockStatus(array &$opencart_product, array $supplier_product_data) {
        $opencart_product['stock_status_id'] = $opencart_product['quantity'] > 0 ? $this->config->get('config_stock_status_id') : $this->config->get('config_out_of_stock_status_id');
    }

    private function _mapProductCategories(array &$opencart_product, array $supplier_product_data) {

        // LogDeveloper("Supplier product data: " . print_r($supplier_product_data, true));

        if (isset($supplier_product_data['Category'])) {
            $main_category = $supplier_product_data['Category'];
            $main_cat_id = isset($main_category['CategoryID']) ? $this->_getXmlNodeValue($main_category['CategoryID']) : '';

            if(!empty($main_cat_id)) {
                $opencart_product['categories_data_source'][] = [
                    // 'id' => $main_cat_id,
                    'name' => isset($main_category['CategoryName']['BG']) ? $this->_getXmlNodeValue($main_category['CategoryName']['BG']) : '',
                    'branch' => isset($main_category['CategoryBranch']['BG']) ? $this->_getXmlNodeValue($main_category['CategoryBranch']['BG']) : ''
                ];
            }
        }
        if (isset($supplier_product_data['AdditionalCategories']['Category'])) {
            $additional_categories = $supplier_product_data['AdditionalCategories']['Category'];
            if (isset($additional_categories['CategoryID']) || (isset($additional_categories[0]) && isset($additional_categories[0]['CategoryID']))) {
                if (!is_array(current($additional_categories)) && isset($additional_categories['CategoryID'])) {
                     $additional_categories = [$additional_categories];
                }
            }

            // Ограничаваме броя допълнителни категории за предотвратяване на memory exhaustion
            // $max_additional_categories = 10; // Максимум 10 допълнителни категории на продукт
            // $categories_count = 0;

            foreach ($additional_categories as $add_cat) {
                // Проверяваме лимита на категориите
                // if ($categories_count >= $max_additional_categories) {
                //     break;
                // }

                $add_cat_id = isset($add_cat['CategoryID']) ? $this->_getXmlNodeValue($add_cat['CategoryID']) : '';
                if(!empty($add_cat_id)) {
                    $opencart_product['categories_data_source'][] = [
                        'id' => $add_cat_id,
                        'name' => isset($add_cat['CategoryName']['BG']) ? $this->_getXmlNodeValue($add_cat['CategoryName']['BG']) : '',
                        'branch' => isset($add_cat['CategoryBranch']['BG']) ? $this->_getXmlNodeValue($add_cat['CategoryBranch']['BG']) : ''
                    ];
                    // $categories_count++;
                }

                // Освобождаваме временните променливи
                unset($add_cat_id);
            }

            // Освобождаваме масива с допълнителни категории
            unset($additional_categories);
        }
    }

    private function _mapProductManufacturer(array &$opencart_product, array $supplier_product_data) {

        $brand_name = isset($supplier_product_data['BrandName']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['BrandName']['BG']) : '';

        if (!empty($brand_name)) {

            $opencart_product['manufacturer_name_source'] = $brand_name;

            // Ако има BrandName, опитваме се да намерим съответната марка
            $language_id = (int)$this->config->get('config_language_id') ?: 1;
            $this->_preloadManufacturers($language_id);

            $normalized_brand_name = mb_strtolower($brand_name);

            if (isset($this->manufacturers_cache[$normalized_brand_name])) {
                // Намерена е съществуваща марка
                $manufacturer = $this->manufacturers_cache[$normalized_brand_name];
                $opencart_product['manufacturer_id'] = $manufacturer['manufacturer_id'];
                // $this->writeToCronLog("eOffice Connector: Намерена марка от BrandName '{$manufacturer['original_name']}' с ID {$manufacturer['manufacturer_id']}", 'mfs_manufacturers.log');
            } else {
                // Създаваме нова марка
                $new_manufacturer_id = $this->_createNewManufacturer($brand_name);
                if ($new_manufacturer_id) {
                    $opencart_product['manufacturer_id'] = $new_manufacturer_id;
                    // Добавяме в кеша
                    $this->manufacturers_cache[$normalized_brand_name] = [
                        'manufacturer_id' => $new_manufacturer_id,
                        'original_name' => $brand_name,
                        'image' => '',
                        'sort_order' => 0
                    ];
                } else {
                    // Fallback към стария начин
                    $opencart_product['manufacturer_name_source'] = $brand_name;
                }
            }
        } else {
            // Няма BrandName, оставяме празно за да се обработи от атрибутите
            $opencart_product['manufacturer_name_source'] = '';
        }
    }

    private function _mapProductAttributes(array &$opencart_product, array $supplier_product_data) {
        if (isset($supplier_product_data['ProductAttributeValues']['ProductAttributeValue'])) {
            $attributes_xml = $supplier_product_data['ProductAttributeValues']['ProductAttributeValue'];

            if (isset($attributes_xml['AttributeCode'])) {
                $attributes_xml = [$attributes_xml];
            }

            // Ограничаваме броя атрибути за предотвратяване на memory exhaustion
            $max_attributes = 50; // Максимум 50 атрибута на продукт
            $attributes_count = 0;
            $skipped_attributes = 0;

            foreach ($attributes_xml as $attr_xml) {
                // Проверяваме лимита на атрибутите
                // if ($attributes_count >= $max_attributes) {
                //     $skipped_attributes++;
                //     continue;
                // }

                $attribute_code = isset($attr_xml['AttributeCode']) ? $this->_getXmlNodeValue($attr_xml['AttributeCode']) : '';
                $attr_value_xml = '';
                if (isset($attr_xml['ProductAttributeValueOptionML']['BG'])) {
                    $attr_value_xml = $this->_getXmlNodeValue($attr_xml['ProductAttributeValueOptionML']['BG']);
                } elseif (isset($attr_xml['ProductAttributeValueText'])) {
                    $attr_value_xml = $this->_getXmlNodeValue($attr_xml['ProductAttributeValueText']);
                }

                if (!empty($attribute_code) && !empty($attr_value_xml)) {
                    // Използваме кеширания масив за намиране на името на атрибута
                    $attribute_name = isset($this->attributes_cache[$attribute_code])
                        ? $this->attributes_cache[$attribute_code]
                        : $attribute_code; // Fallback към кода ако няма име в кеша

                    // Ограничаваме дължината на стойностите за спестяване на памет
                    $attr_name_trimmed = mb_strlen($attribute_name) > 100 ? mb_substr($attribute_name, 0, 100) . '...' : $attribute_name;
                    $attr_value_trimmed = mb_strlen($attr_value_xml) > 500 ? mb_substr($attr_value_xml, 0, 500) . '...' : $attr_value_xml;

                    $opencart_product['attributes_data_source'][] = [
                        'name'  => $attr_name_trimmed,
                        'value' => $attr_value_trimmed
                    ];
                    $attributes_count++;
                }

                // Освобождаваме временните променливи
                unset($attribute_code, $attr_value_xml, $attribute_name, $attr_name_trimmed, $attr_value_trimmed);
            }

            // Логваме ако са пропуснати атрибути
            if ($skipped_attributes > 0) {
                // $this->writeToCronLog("eOffice Connector: Пропуснати {$skipped_attributes} атрибута за продукт поради лимит от {$max_attributes} атрибута", 'mfs_attributes.log');
            }

            // Освобождаваме масива с атрибути от XML
            unset($attributes_xml);
        }
    }

    private function _mapProductSeoData(array &$opencart_product, array $supplier_product_data) {
        if(isset($supplier_product_data['SEO'])) {
            $seo_title = isset($supplier_product_data['SEO']['Title']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['SEO']['Title']['BG']) : '';
            if(!empty($seo_title)) {
                $opencart_product['meta_title'] = $seo_title;
            }

            $seo_keywords = isset($supplier_product_data['SEO']['MetaKeywords']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['SEO']['MetaKeywords']['BG']) : '';
            if(!empty($seo_keywords)) {
                $opencart_product['meta_keyword'] = $seo_keywords;
            }

            $meta_desc_candidate = isset($supplier_product_data['SEO']['MetaDescription']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['SEO']['MetaDescription']['BG']) : '';
            if(trim($meta_desc_candidate) !== '') {
                $opencart_product['meta_description'] = $meta_desc_candidate;
            } elseif (isset($supplier_product_data['ProductDescription']['BG']) && empty($opencart_product['meta_description'])) {
                $product_desc_for_meta = $this->_getXmlNodeValue($supplier_product_data['ProductDescription']['BG']);
                $opencart_product['meta_description'] = strip_tags(html_entity_decode($product_desc_for_meta));
                if(mb_strlen($opencart_product['meta_description']) > 255) {
                    $opencart_product['meta_description'] = mb_substr($opencart_product['meta_description'], 0, 252) . '...';
                }
            }
        }
    }

    private function _mapProductOptions(array &$opencart_product, array $supplier_product_data, array $full_feed_data) {
        // Логиката за мапиране на опции остава непроменена
    }

    /**
     * Извлича глобални данни (напр. OptionGroups) от XML низ.
     * @param string $xml_data_source XML низ.
     * @param string $global_node_name Име на глобалния XML възел.
     * @param string $c_key Ключ на конектора.
     * @param string &$current_log Текущ лог.
     * @param string $log_entry_prefix Префикс за лог записите.
     * @return array Масив с глобални данни или празен масив при грешка/липса.
     */
    private function _extractGlobalDataFromXml(string $xml_data_source, string $global_node_name, string $c_key, &$current_log, string $log_entry_prefix) {
        $global_data_array = [];
        $xml_reader_global = new \XMLReader();

        if ($xml_reader_global->XML($xml_data_source)) {
            while ($xml_reader_global->read()) {
                if ($xml_reader_global->nodeType == \XMLReader::ELEMENT && $xml_reader_global->name == $global_node_name) {
                    $global_node_xml_string = $xml_reader_global->readOuterXML();
                    if ($global_node_xml_string) {
                        $global_data_candidate = $this->convertXMLdataToArray($global_node_xml_string);
                        if ($global_data_candidate && is_array($global_data_candidate)){
                            $global_data_array = $global_data_candidate;
                        }
                    }
                    break;
                }
            }
            $xml_reader_global->close();
        } else {
            $current_log .= $log_entry_prefix . "Грешка: Неуспешно зареждане на XML за извличане на глобални данни ({$global_node_name}) за {$c_key}.\n";
        }
        return $global_data_array;
    }

    /**
     * Предварително зарежда всички атрибути от XML файла в кеш
     * @param string $xml_data XML данни
     * @return bool True при успех, false при грешка
     */
    private function _preloadAttributesFromXml($xml_data) {
        if ($this->attributes_cache_loaded) {
            return true; // Вече са заредени
        }

        try {
            // Поточно четене на XML за извличане на атрибути
            $xml_reader = new \XMLReader();
            if (!$xml_reader->XML($xml_data)) {
                return false;
            }

            // Навигиране до Attributes секцията
            while ($xml_reader->read() && !($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'Attributes')) {
                // Продължаваме да четем
            }

            if ($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'Attributes') {
                while ($xml_reader->read()) {
                    if ($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'Attribute') {
                        $node = @$xml_reader->expand();
                        if ($node) {
                            $attribute_data = $this->_convertDomNodeToArray($node);

                            // Извличаме AttributeCode и AttributeName[BG]
                            if (isset($attribute_data['AttributeCode']) && isset($attribute_data['AttributeName']['BG'])) {
                                $attribute_code = $this->_getXmlNodeValue($attribute_data['AttributeCode']);
                                $attribute_name_bg = $this->_getXmlNodeValue($attribute_data['AttributeName']['BG']);

                                if (!empty($attribute_code) && !empty($attribute_name_bg)) {
                                    // Кешираме с нормализиран ключ за бързо търсене
                                    $this->attributes_cache[$attribute_code] = $attribute_name_bg;
                                }
                            }
                        }
                    } elseif ($xml_reader->nodeType == \XMLReader::END_ELEMENT && $xml_reader->name == 'Attributes') {
                        break;
                    }
                }
            }
            $xml_reader->close();

            $this->attributes_cache_loaded = true;
            return true;

        } catch (Exception $e) {
            // В случай на грешка връщаме false
            return false;
        }
    }

    /**
     * Предварително зарежда всички OpenCart атрибути в кеш
     * @param int $language_id ID на езика
     * @return bool True при успех, false при грешка
     */
    private function _preloadOpenCartAttributes($language_id) {
        if ($this->opencart_attributes_cache_loaded) {
            return true; // Вече са заредени
        }

        try {
            // Зареждаме всички OpenCart атрибути
            $query = $this->db->query("
                SELECT a.attribute_id, ad.name, a.attribute_group_id
                FROM `" . DB_PREFIX . "attribute` a
                LEFT JOIN `" . DB_PREFIX . "attribute_description` ad
                    ON (a.attribute_id = ad.attribute_id)
                WHERE ad.language_id = '" . (int)$language_id . "'
            ");

            foreach ($query->rows as $attribute) {
                // Използваме нормализирано име като ключ за бързо търсене
                $normalized_name = mb_strtolower($attribute['name']);
                $this->opencart_attributes_cache[$normalized_name] = [
                    'attribute_id' => $attribute['attribute_id'],
                    'attribute_group_id' => $attribute['attribute_group_id'],
                    'original_name' => $attribute['name']
                ];
            }

            $this->opencart_attributes_cache_loaded = true;
            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Предварително зарежда всички марки/производители от OpenCart в кеш
     * @param int $language_id ID на езика
     * @return bool True при успех, false при грешка
     */
    private function _preloadManufacturers($language_id) {
        if ($this->manufacturers_cache_loaded) {
            return true; // Вече са заредени
        }

        try {
            // Зареждаме всички OpenCart марки/производители
            $query = $this->db->query("
                SELECT m.manufacturer_id, m.name, m.image, m.sort_order
                FROM `" . DB_PREFIX . "manufacturer` m
                ORDER BY m.name ASC
            ");

            foreach ($query->rows as $manufacturer) {
                // Използваме нормализирано име като ключ за бързо търсене
                $normalized_name = mb_strtolower($manufacturer['name']);
                $this->manufacturers_cache[$normalized_name] = [
                    'manufacturer_id' => $manufacturer['manufacturer_id'],
                    'original_name' => $manufacturer['name'],
                    'image' => $manufacturer['image'],
                    'sort_order' => $manufacturer['sort_order']
                ];
            }

            $this->manufacturers_cache_loaded = true;
            $this->writeToCronLog("eOffice Connector: Заредени " . count($this->manufacturers_cache) . " марки в кеша.", 'mfs_manufacturers.log');
            return true;

        } catch (Exception $e) {
            $this->writeToCronLog("eOffice Connector: Грешка при зареждане на марки в кеша: " . $e->getMessage(), 'mfs_manufacturers.log');
            return false;
        }
    }

    /**
     * Обработва атрибутите на продукт и връща готови данни за основния модел
     * @param array $product_data Данни на продукта
     * @param int $language_id ID на езика
     * @return array Масив с обработени атрибути готови за основния модел
     */
    public function processProductAttributes($product_data, $language_id = 1) {
        $processed_attributes = [];

            // $this->writeToCronLog("MultiFeed Syncer: Processing product SKU " . $product_data['sku'] . " for debugging", 'mfs_debug.log');

        if (!isset($product_data['attributes_data_source']) || empty($product_data['attributes_data_source'])) {

            // $this->writeToCronLog("MultiFeed Syncer: No attributes_data_source for product SKU " . $product_data['sku'], 'mfs_debug.log');

            return $processed_attributes;
        }

        // Предварително зареждаме OpenCart атрибутите ако не са заредени
        $this->_preloadOpenCartAttributes($language_id);

        // Получаваме атрибутната група по подразбиране
        $default_attribute_group_id = $this->_getDefaultAttributeGroupId($language_id);

        
            // $this->writeToCronLog("MultiFeed Syncer: Default attribute group ID: " . $default_attribute_group_id, 'mfs_debug.log');
            // $this->writeToCronLog("MultiFeed Syncer: attributes_data_source: " . print_r($product_data['attributes_data_source'], true), 'mfs_debug.log');
        

        foreach ($product_data['attributes_data_source'] as $attribute) {
            if (empty($attribute['name']) || empty($attribute['value'])) {
                continue;
            }

            // Нормализираме името за търсене в кеша
            $normalized_name = mb_strtolower($attribute['name']);

            
            // $this->writeToCronLog("MultiFeed Syncer: Normalized attribute name: " . $normalized_name, 'mfs_debug.log');
            

            // Търсим в кеша на OpenCart атрибути
            if (isset($this->opencart_attributes_cache[$normalized_name])) {
                // Намерен е съществуващ атрибут
                $oc_attribute = $this->opencart_attributes_cache[$normalized_name];

            
                    // $this->writeToCronLog("MultiFeed Syncer: Found attribute ID " . $oc_attribute['attribute_id'] . " for name " . $oc_attribute['original_name'], 'mfs_debug.log');

                    if(isset($processed_attributes[ $oc_attribute['attribute_id'] ])) {
                        // $this->writeToCronLog("MultiFeed Syncer: Attribute ID " . $oc_attribute['attribute_id'] . " already exists in processed attributes", 'mfs_debug.log');
                        continue;
                    }
            

                $processed_attributes[ $oc_attribute['attribute_id'] ] = [
                    'attribute_id' => $oc_attribute['attribute_id'],
                    'name' => $oc_attribute['original_name'],
                    'value' => $attribute['value'],
                    'attribute_group_id' => $oc_attribute['attribute_group_id']
                ];
            } else {
                // Трябва да се създаде нов атрибут
                $new_attribute_id = $this->_createNewAttribute($attribute['name'], $default_attribute_group_id, $language_id);

                
                    // $this->writeToCronLog("MultiFeed Syncer: Created new attribute ID " . $new_attribute_id . " for name " . $attribute['name'], 'mfs_debug.log');
                

                if ($new_attribute_id) {
                    // Добавяме новия атрибут в кеша
                    $this->opencart_attributes_cache[$normalized_name] = [
                        'attribute_id' => $new_attribute_id,
                        'attribute_group_id' => $default_attribute_group_id,
                        'original_name' => $attribute['name']
                    ];

                    $processed_attributes[ $new_attribute_id ] = [
                        'attribute_id' => $new_attribute_id,
                        'name' => $attribute['name'],
                        'value' => $attribute['value'],
                        'attribute_group_id' => $default_attribute_group_id
                    ];
                }
            }
        }

        
         // $this->writeToCronLog("MultiFeed Syncer: Processed attributes: " . print_r($processed_attributes, true), 'mfs_debug.log');
        

        return array_values($processed_attributes);
    }

    /**
     * Създава нова марка/производител в OpenCart
     * @param string $manufacturer_name Име на марката
     * @return int|false ID на новата марка или false при грешка
     */
    private function _createNewManufacturer($manufacturer_name) {
        try {
            // Създаваме основния запис
            $this->db->query("
                INSERT INTO `" . DB_PREFIX . "manufacturer`
                SET `name` = '" . $this->db->escape($manufacturer_name) . "',
                    `image` = '',
                    `sort_order` = '0'
            ");

            $manufacturer_id = $this->db->getLastId();

            if ($manufacturer_id) {
                $this->writeToCronLog("eOffice Connector: Създадена нова марка '{$manufacturer_name}' с ID {$manufacturer_id}", 'mfs_manufacturers.log');
                return $manufacturer_id;
            }

            return false;

        } catch (Exception $e) {
            $this->writeToCronLog("eOffice Connector: Грешка при създаване на марка '{$manufacturer_name}': " . $e->getMessage(), 'mfs_manufacturers.log');
            return false;
        }
    }

    /**
     * Обработва марката на продукт от атрибутите и връща manufacturer_id
     * @param array $product_data Данни на продукта
     * @return int|null ID на марката или null ако не е намерена
     */
    public function processProductManufacturer(&$product_data) {

        if (isset($product_data['manufacturer_id']) && !empty($product_data['manufacturer_id'])) {
            return $product_data['manufacturer_id'];
        }

        // Предварително зареждаме марките ако не са заредени
        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        $this->_preloadManufacturers($language_id);

        if (!isset($product_data['attributes_data_source']) || empty($product_data['attributes_data_source'])) {
            return null;
        }

        // Търсим атрибут с име 'brand' в атрибутите на продукта
        foreach ($product_data['attributes_data_source'] as $attribute) {
            if (empty($attribute['name']) || empty($attribute['value'])) {
                continue;
            }

            // Проверяваме дали атрибутът е марка (brand)
            $attribute_name = mb_strtolower($attribute['name']);
            if ($attribute_name === 'brand' || $attribute_name === 'марка' || $attribute_name === 'производител') {
                $brand_name = trim($attribute['value']);

                if (empty($brand_name)) {
                    continue;
                }

                // $this->writeToCronLog("eOffice Connector: Намерена марка '{$brand_name}' в атрибутите на продукта", 'mfs_manufacturers.log');

                // Нормализираме името за търсене в кеша
                $normalized_brand_name = mb_strtolower($brand_name);

                // Търсим в кеша на марки
                if (isset($this->manufacturers_cache[$normalized_brand_name])) {
                    // Намерена е съществуваща марка
                    $manufacturer = $this->manufacturers_cache[$normalized_brand_name];
                    $this->writeToCronLog("eOffice Connector: Намерена съществуваща марка '{$manufacturer['original_name']}' с ID {$manufacturer['manufacturer_id']}, за продукт SKU {$product_data['sku']}", 'mfs_manufacturers.log');
                    $product_data['manufacturer'] = $manufacturer['original_name'];
                    return $manufacturer['manufacturer_id'];
                } else {
                    // Трябва да се създаде нова марка
                    $new_manufacturer_id = $this->_createNewManufacturer($brand_name);

                    if ($new_manufacturer_id) {
                        // Добавяме новата марка в кеша
                        $this->manufacturers_cache[$normalized_brand_name] = [
                            'manufacturer_id' => $new_manufacturer_id,
                            'original_name' => $brand_name,
                            'image' => '',
                            'sort_order' => 0
                        ];

                        $this->writeToCronLog("eOffice Connector: Създадена и кеширана нова марка '{$brand_name}' с ID {$new_manufacturer_id}", 'mfs_manufacturers.log');
                        return $new_manufacturer_id;
                    }
                }
            }
        }

        return null; // Не е намерена марка
    }

    private function writeToCronLog($message, $log_file=null) {
        $log_file = $log_file ? DIR_LOGS . $log_file : DIR_LOGS . 'multi_feed_syncer.log'; 
        $time = date('Y-m-d H:i:s');
        file_put_contents($log_file, $time . ': ' . $message . PHP_EOL, FILE_APPEND);
    }

    /**
     * Получава ID на атрибутната група по подразбиране
     * @param int $language_id ID на езика
     * @return int ID на атрибутната група
     */
    private function _getDefaultAttributeGroupId($language_id) {
        // Търсим група с име "General" или "Общи"
        $query = $this->db->query("
            SELECT ag.attribute_group_id
            FROM `" . DB_PREFIX . "attribute_group` ag
            LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd
                ON (ag.attribute_group_id = agd.attribute_group_id)
            WHERE agd.language_id = '" . (int)$language_id . "'
                AND (agd.name = 'General' OR agd.name = 'Общи' OR agd.name = 'Default')
            LIMIT 1
        ");

        if ($query->num_rows) {
            return $query->row['attribute_group_id'];
        }

        // Ако няма такава група, създаваме я
        return $this->_createDefaultAttributeGroup($language_id);
    }

    /**
     * Създава атрибутна група по подразбиране
     * @param int $language_id ID на езика
     * @return int ID на новосъздадената група
     */
    private function _createDefaultAttributeGroup($language_id) {
        // Създаваме основния запис
        $this->db->query("
            INSERT INTO `" . DB_PREFIX . "attribute_group`
            SET `sort_order` = '1'
        ");

        $group_id = $this->db->getLastId();

        if ($group_id) {
            // Създаваме описанието
            $this->db->query("
                INSERT INTO `" . DB_PREFIX . "attribute_group_description`
                SET `attribute_group_id` = '" . (int)$group_id . "',
                    `language_id` = '" . (int)$language_id . "',
                    `name` = 'Общи'
            ");
        }

        return $group_id;
    }

    /**
     * Създава нов атрибут в OpenCart
     * @param string $attribute_name Име на атрибута
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return int|false ID на новия атрибут или false при грешка
     */
    private function _createNewAttribute($attribute_name, $attribute_group_id, $language_id) {
        try {
            // Създаваме основния запис
            $this->db->query("
                INSERT INTO `" . DB_PREFIX . "attribute`
                SET `attribute_group_id` = '" . (int)$attribute_group_id . "',
                    `sort_order` = '0'
            ");

            $attribute_id = $this->db->getLastId();

            if ($attribute_id) {
                // Създаваме описанието
                $this->db->query("
                    INSERT INTO `" . DB_PREFIX . "attribute_description`
                    SET `attribute_id` = '" . (int)$attribute_id . "',
                        `language_id` = '" . (int)$language_id . "',
                        `name` = '" . $this->db->escape($attribute_name) . "'
                ");

                return $attribute_id;
            }

            return false;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Извлича всички уникални пътища на категории от синхронизиращия файл
     * @return array Масив с уникални пътища на категории
     */
    public function getCategoriesFromFile() {
        $categories = [];

        try {
            
            $xml_data = $this->requestCachedSyncData();
            if(!$xml_data){
                $xml_data = $this->requestSyncData();
                $this->setCachedSyncData($xml_data);
            }
            if (!$xml_data) {
                return [];
            }

            // Предварително зареждане на атрибути от XML
            $this->_preloadAttributesFromXml($xml_data);

            // Предварително зареждане на марки от OpenCart
            $language_id = (int)$this->config->get('config_language_id') ?: 1;
            $this->_preloadManufacturers($language_id);

            // Поточно четене на XML за извличане на категории
            $xml_reader = new \XMLReader();
            if (!$xml_reader->XML($xml_data)) {
                return [];
            }

            // Навигиране до родителския елемент на продуктите
            while ($xml_reader->read() && !($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'Products')) {
                // Продължаваме да четем
            }

            if ($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'Products') {
                while ($xml_reader->read()) {
                    if ($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'Product') {
                        $node = @$xml_reader->expand();
                        if ($node) {
                            $product_data = $this->_convertDomNodeToArray($node);

                            // Извличаме категорията от основната категория
                            if (isset($product_data['Category']['CategoryBranch']['BG'])) {
                                $category_branch = $this->_getXmlNodeValue($product_data['Category']['CategoryBranch']['BG']);
                                if (!empty($category_branch)) {
                                    // Заменяме разделителя "|" с " > " за по-добра четимост
                                    $category_path = str_replace('|', ' > ', $category_branch);
                                    $categories[$category_path] = true;

                                    // Добавяме и всички родителски категории
                                    $this->_addParentCategories($category_branch, $categories);
                                }
                            }

                            // Извличаме категориите от допълнителните категории
                            if (isset($product_data['AdditionalCategories']['Category'])) {
                                $additional_categories = $product_data['AdditionalCategories']['Category'];

                                // Проверяваме дали е единична категория или масив от категории
                                if (isset($additional_categories['CategoryBranch'])) {
                                    // Единична категория
                                    $additional_categories = [$additional_categories];
                                }

                                foreach ($additional_categories as $add_cat) {
                                    if (isset($add_cat['CategoryBranch']['BG'])) {
                                        $category_branch = $this->_getXmlNodeValue($add_cat['CategoryBranch']['BG']);
                                        if (!empty($category_branch)) {
                                            // Заменяме разделителя "|" с " > " за по-добра четимост
                                            $category_path = str_replace('|', ' > ', $category_branch);
                                            $categories[$category_path] = true;

                                            // Добавяме и всички родителски категории
                                            $this->_addParentCategories($category_branch, $categories);
                                        }
                                    }
                                }
                            }
                        }
                    } elseif ($xml_reader->nodeType == \XMLReader::END_ELEMENT && $xml_reader->name == 'Products') {
                        break;
                    }
                }
            }
            $xml_reader->close();

        } catch (Exception $e) {
            // В случай на грешка връщаме празен масив
            return [];
        }

        // Сортираме категориите по азбучен ред
        $sorted_categories = array_keys($categories);
        sort($sorted_categories);

        return $sorted_categories;
    }

    /**
     * Добавя всички родителски категории към масива с категории
     * @param string $category_branch Пълният път на категорията разделен с "|"
     * @param array &$categories Масив с категории (по референция)
     */
    private function _addParentCategories($category_branch, &$categories) {
        $parts = explode('|', $category_branch);
        $current_path = '';

        foreach ($parts as $part) {
            $part = trim($part);
            if (!empty($part)) {
                if (!empty($current_path)) {
                    $current_path .= ' > ';
                }
                $current_path .= $part;
                $categories[$current_path] = true;
            }
        }
    }



    /**
     * Получава информация за последното изтегляне на файла
     * @return array
     */
    public function getLastFileDownloadInfo() {
        $file_path = $this->_getLocalFilePath();

        if (file_exists($file_path)) {
            $file_time = filemtime($file_path);
            return [
                'file_exists' => true,
                'last_modified' => date('d.m.Y H:i:s', $file_time),
                'file_size' => $this->_formatFileSize(filesize($file_path))
            ];
        } else {
            return [
                'file_exists' => false,
                'last_modified' => null,
                'file_size' => null
            ];
        }
    }

    /**
     * Изтегля отново файла
     * @return bool
     */
    public function downloadFileAgain() {
        try {
            $xml_data = $this->requestSyncData();
            if ($xml_data) {
                $file_path = $this->_getLocalFilePath();
                $dir = dirname($file_path);

                // Създаваме директорията, ако не съществува
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                }

                // Изтриваме кеширания файл с обработени продукти при изтегляне на нова версия
                $this->_clearCachedProducts();

                // Записваме файла
                return file_put_contents($file_path, $xml_data) !== false;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Получава пътя до локалния файл
     * @return string
     */
    private function _getLocalFilePath() {
        $storage_dir = DIR_STORAGE . 'multi_feed_syncer/' . $this->connector_key . '/';
        return $storage_dir . 'feed_data.xml';
    }

    /**
     * Форматира размера на файла
     * @param int $size
     * @return string
     */
    private function _formatFileSize($size) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unit_index = 0;

        while ($size >= 1024 && $unit_index < count($units) - 1) {
            $size /= 1024;
            $unit_index++;
        }

        return round($size, 2) . ' ' . $units[$unit_index];
    }

    /**
     * Обработва XML фийд данни, мапира продукти и ги връща като масив.
     * @param string $supplier_data_source XML низ с данните от фийда.
     * @param string $c_key Ключ на конектора.
     * @param string &$current_log Текущ лог.
     * @param array &$sync_stats Масив със статистики (обновява се по референция).
     * @param string $log_entry_prefix Префикс за лог записите.
     * @return array Масив с мапирани OpenCart продукти.
     */
    private function _processXmlFeedData(string $supplier_data_source, string $c_key, &$current_log, &$sync_stats, string $log_entry_prefix) {
        $opencart_products_batch = [];
        $global_xml_data_array = [];

        $xml_product_node_name = 'Product';
        $xml_products_parent_node_name = 'Products';
        $xml_option_groups_node_name = 'OptionGroups'; // Име на възела за глобални данни

        if (empty($supplier_data_source)) {
            $current_log .= $log_entry_prefix . "Грешка: XML източникът на данни е празен за {$c_key}.\n";
            $sync_stats['error_message'] = "XML източникът на данни е празен";
            return $opencart_products_batch;
        }

        // 1. Извличане на глобални данни (OptionGroups)
        $global_xml_data_array = $this->_extractGlobalDataFromXml($supplier_data_source, $xml_option_groups_node_name, $c_key, $current_log, $log_entry_prefix);

        // 1.1. Предварително зареждане на атрибути от XML
        if ($this->_preloadAttributesFromXml($supplier_data_source)) {
            $current_log .= $log_entry_prefix . "Заредени " . count($this->attributes_cache) . " атрибута от XML в кеша.\n";
        } else {
            $current_log .= $log_entry_prefix . "Предупреждение: Неуспешно зареждане на атрибути от XML.\n";
        }

        // 1.2. Предварително зареждане на марки от OpenCart
        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        if ($this->_preloadManufacturers($language_id)) {
            $current_log .= $log_entry_prefix . "Заредени " . count($this->manufacturers_cache) . " марки в кеша.\n";
        } else {
            $current_log .= $log_entry_prefix . "Предупреждение: Неуспешно зареждане на марки.\n";
        }

        // 2. Поточно четене и обработка на продуктите
        $xml_reader_products = new \XMLReader();
        if (!$xml_reader_products->XML($supplier_data_source)) {
            $current_log .= $log_entry_prefix . "Грешка: Неуспешно зареждане на XML за обработка на продукти за {$c_key}.\n";
            $sync_stats['error_message'] = "Неуспешно зареждане на XML за продукти";
            return $opencart_products_batch;
        }

        $found_products_parent = false;
        // Навигиране до родителския елемент на продуктите
        while ($xml_reader_products->read() && !($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == $xml_products_parent_node_name)) {
            // Продължаваме да четем
        }

        if ($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == $xml_products_parent_node_name) {
            $found_products_parent = true;
        }

        if ($found_products_parent) {
            // Константи за управление на паметта - още по-агресивни настройки
            $memory_batch_size = 5; // Обработваме по 5 продукта наведнъж (намалено от 10)
            $memory_limit_check_mb = 30; // Проверяваме паметта при достигане на 30MB (намалено от 50MB)
            $products_processed = 0;
            $memory_start = memory_get_usage(true);

            // Ограничаваме общия брой продукти за обработка ако паметта е ограничена
            //$max_products_to_process = 500; // Максимум 500 продукта за обработка
            $max_products_to_process = 0; // Без ограничение
            $products_skipped_due_to_limit = 0;

            while ($xml_reader_products->read()) {
                if ($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == $xml_product_node_name) {
                    // Проверяваме дали сме достигнали лимита на продукти
                    if ($max_products_to_process > 0 && $products_processed >= $max_products_to_process) {
                        $products_skipped_due_to_limit++;
                        continue; // Пропускаме останалите продукти
                    }

                    $node = @$xml_reader_products->expand();
                    if ($node) {
                        $supplier_product_data = $this->_convertDomNodeToArray($node);
                        if ($supplier_product_data && is_array($supplier_product_data)) {
                            $opencart_product_data = $this->mapDataToOpencartProduct($supplier_product_data, $global_xml_data_array);
                            if ($opencart_product_data) {
                                $opencart_products_batch[] = $opencart_product_data;
                            } else {
                                $sync_stats['skipped']++;
                                $current_log .= $log_entry_prefix . "Продукт пропуснат по време на мапиране (XML). Данни: " . mb_substr(print_r($supplier_product_data, true), 0, 200) . "...\n";
                            }
                        } else {
                            $current_log .= $log_entry_prefix . "Грешка: Неуспешно конвертиране на продуктов XML възел в масив.\n";
                            $sync_stats['errors']++;
                        }

                        // Освобождаваме паметта за обработения възел
                        unset($supplier_product_data, $opencart_product_data, $node);
                        $products_processed++;

                        // Проверка на паметта и принудително освобождаване при нужда
                        if ($products_processed % $memory_batch_size === 0) {
                            $memory_current = memory_get_usage(true);
                            $memory_used_mb = ($memory_current - $memory_start) / 1024 / 1024;

                            if ($memory_used_mb > $memory_limit_check_mb) {
                                $current_log .= $log_entry_prefix . "Принудително освобождаване на памет след {$products_processed} продукта. Използвана памет: " . round($memory_used_mb, 2) . "MB\n";

                                // Принудително освобождаване на паметта
                                if (function_exists('gc_collect_cycles')) {
                                    gc_collect_cycles();
                                }

                                $memory_start = memory_get_usage(true); // Нулираме отчета
                            }
                        }
                    }
                } elseif ($xml_reader_products->nodeType == \XMLReader::END_ELEMENT && $xml_reader_products->name == $xml_products_parent_node_name) {
                    break;
                }
            }

            // Логваме ако са пропуснати продукти поради лимит
            if ($products_skipped_due_to_limit > 0) {
                $current_log .= $log_entry_prefix . "ПРЕДУПРЕЖДЕНИЕ: Пропуснати {$products_skipped_due_to_limit} продукта поради лимит от {$max_products_to_process} продукта за memory optimization.\n";
            }

            $current_log .= $log_entry_prefix . "Обработени общо {$products_processed} продукта от XML фийда.\n";
        } else {
            $current_log .= $log_entry_prefix . "Предупреждение: Не е намерен родителски елемент '{$xml_products_parent_node_name}' за продуктите в XML фийда за {$c_key}.\n";
        }
        $xml_reader_products->close();

        // Финално освобождаване на паметта
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        return $opencart_products_batch;
    }

    /**
     * Обработва фийд данни във формат масив (напр. от JSON), мапира продукти и ги връща.
     * @param array $supplier_data_source Масив с данните от фийда (очаква се масив от продукти).
     * @param string $c_key Ключ на конектора.
     * @param string &$current_log Текущ лог.
     * @param array &$sync_stats Масив със статистики (обновява се по референция).
     * @param string $log_entry_prefix Префикс за лог записите.
     * @return array Масив с мапирани OpenCart продукти.
     */
    private function _processArrayFeedData(array $supplier_data_source, string $c_key, &$current_log, &$sync_stats, string $log_entry_prefix) {
        $opencart_products_batch = [];

        if (!method_exists($this, 'mapDataToOpencartProduct')) {
            $current_log .= $log_entry_prefix . "Грешка: Методът mapDataToOpencartProduct липсва в {$c_key} конектора.\n";
            $sync_stats['error_message'] = "Методът mapDataToOpencartProduct липсва";
            return $opencart_products_batch;
        }

        // Приемаме, че $supplier_data_source е масив от продукти, ако не е XML.
        // Ако JSON структурата е по-сложна (напр. продуктите са вложен ключ),
        // $supplier_data_source трябва да сочи директно към масива с продукти преди извикването на този метод.
        // Засега, според оригиналния код, $supplier_data_source е директно масива от продукти.
        $products_from_feed = $supplier_data_source;

        if (empty($products_from_feed)) {
            $current_log .= $log_entry_prefix . "Няма намерени продукти във фийда (JSON/масив) за {$c_key} за мапиране.\n";
            if (empty($sync_stats['error_message']) && empty($sync_stats['message'])) {
                $sync_stats['message'] = 'Няма продукти във фийда (JSON/масив) за мапиране';
            }
        } else {
            foreach ($products_from_feed as $product_data_item) {
                if (!is_array($product_data_item)) {
                    $current_log .= $log_entry_prefix . "Предупреждение: Елемент от фийда (JSON/масив) не е масив и ще бъде пропуснат. Данни: " . print_r($product_data_item, true) . "\n";
                    $sync_stats['skipped']++;
                    continue;
                }
                // Целият $supplier_data_source (който е масив от продукти или по-сложна структура с глобални данни)
                // се подава като $full_feed_data на mapDataToOpencartProduct.
                $opencart_product_data = $this->mapDataToOpencartProduct($product_data_item, $supplier_data_source);
                if ($opencart_product_data) {
                    $opencart_products_batch[] = $opencart_product_data;
                } else {
                    $sync_stats['skipped']++;
                    $current_log .= $log_entry_prefix . "Продукт пропуснат по време на мапиране (JSON/масив). Данни: " . mb_substr(print_r($product_data_item, true), 0, 200) . "...\n";
                }
            }
        }
        return $opencart_products_batch;
    }

    private $current_log = '';
    private $mfs_model_instance;

    public function processSupplierFeed($c_key, $mfsc_id, $testmode = false, $mfs_model_instance) {

        // Мониторинг на паметта в началото
        $memory_start = memory_get_usage(true);
        $memory_limit = ini_get('memory_limit');
        $memory_limit_bytes = $this->_parseMemoryLimit($memory_limit);

        if(is_object($mfs_model_instance)) {
            $this->mfs_model_instance = $mfs_model_instance;
        }
        else {
            $this->mfs_model_instance = $this->load->model('extension/module/multi_feed_syncer');
            $this->mfs_model_instance->setTestMode($testmode);
        }

        $log_entry_prefix = "Конектор ({$c_key}): ";

        $current_log = &$this->current_log;
        $sync_stats = [
            'added' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'message' => '',
            'error_message' => '',
            'connection_established' => false
        ];
        $opencart_products_batch = [];

        // Логваме началните параметри на паметта
        $memory_start_mb = $memory_start / 1024 / 1024;
        $memory_limit_mb = $memory_limit_bytes / 1024 / 1024;
        $current_log .= $log_entry_prefix . "Стартова памет: " . round($memory_start_mb, 2) . "MB, Лимит: " . round($memory_limit_mb, 2) . "MB\n";

        // Стъпка 1: Проверка за кеширани обработени продукти преди извличане на данни
        $opencart_products_batch = $this->_loadCachedProducts();

        if ($opencart_products_batch !== false) {
            // Имаме валидни кеширани продукти, пропускаме извличането и обработката на XML данните
            $current_log .= $log_entry_prefix . "Заредени " . count($opencart_products_batch) . " продукта от кеша. Пропускаме извличането на XML данни.\n";
            $sync_stats['connection_established'] = true; // Считаме връзката за установена, тъй като имаме кеширани данни
        } else {
            // Няма кеширани данни, извличаме и обработваме XML данните
            $current_log .= $log_entry_prefix . "Няма кеширани обработени продукти, започваме извличане и обработка на XML данните.\n";

            // Стъпка 2: Извличане и подготовка на данните
            list($supplier_data_source, $data_is_xml, $connection_ok, $conversion_error) = $this->_requestAndPrepareData($c_key, $current_log, $sync_stats, $log_entry_prefix);
            $sync_stats['connection_established'] = $connection_ok;

            if (!$connection_ok || $conversion_error) {
                // Грешката вече е логната и записана в $sync_stats от _requestAndPrepareData
                return $sync_stats;
            }

            if (empty($supplier_data_source) && !$conversion_error) {
                $current_log .= $log_entry_prefix . "Предупреждение: Източникът на данни е празен за {$c_key}.\n";
                $sync_stats['error_message'] = "Източникът на данни е празен";
                if (empty($sync_stats['message'])) {
                     $sync_stats['message'] = 'Източникът на данни е празен.';
                }
                return $sync_stats;
            }

            // Стъпка 3: Мапиране на продуктите
            if ($data_is_xml && is_string($supplier_data_source)) {
                $opencart_products_batch = $this->_processXmlFeedData($supplier_data_source, $c_key, $current_log, $sync_stats, $log_entry_prefix);

                // Записваме обработените продукти в кеша
                if (!empty($opencart_products_batch)) {
                    $this->_saveCachedProducts($opencart_products_batch);
                    $current_log .= $log_entry_prefix . "Обработените продукти са записани в кеша за бъдещо използване.\n";
                }
            } elseif (!$data_is_xml && is_array($supplier_data_source)) {
                $opencart_products_batch = $this->_processArrayFeedData($supplier_data_source, $c_key, $current_log, $sync_stats, $log_entry_prefix);
            } else {
                // Този else блок е за непредвидени случаи, които би трябвало да са хванати по-рано.
                if (empty($sync_stats['error_message'])) { // Записваме грешка само ако няма вече такава
                    $current_log .= $log_entry_prefix . "Грешка: Неочакван тип на данните или проблем с източника след подготовка за {$c_key}. Тип XML: " . ($data_is_xml ? 'Да' : 'Не') . "\n";
                    $sync_stats['error_message'] = "Неочакван тип на данните или празен източник след подготовка.";
                }
                return $sync_stats;
            }
        }

        $current_log .= $log_entry_prefix . count($opencart_products_batch) . " продукта са мапирани за {$c_key}.\n";

        if (empty($opencart_products_batch)) {
            if (empty($sync_stats['error_message']) && empty($sync_stats['message'])) {
                $current_log .= $log_entry_prefix . "Няма продукти за обработка от фийда за {$c_key} след мапиране.\n";
                $sync_stats['message'] = 'Няма продукти във фийда за обработка';
            }
            // Връщаме $sync_stats, които може да съдържат грешки/съобщения от мапирането
            return $sync_stats;
        }

        // Проверка на паметта преди започване на синхронизацията
        $memory_before_sync = memory_get_usage(true);
        $memory_before_sync_mb = $memory_before_sync / 1024 / 1024;

        if ($memory_before_sync_mb > ($memory_limit_mb * 0.8)) {
            $current_log .= $log_entry_prefix . "КРИТИЧНО ПРЕДУПРЕЖДЕНИЕ: Паметта е на " . round(($memory_before_sync_mb / $memory_limit_mb) * 100, 1) . "% от лимита преди синхронизация!\n";

            // Принудително освобождаване на паметта преди синхронизация
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memory_after_gc = memory_get_usage(true);
            $memory_after_gc_mb = $memory_after_gc / 1024 / 1024;
            $current_log .= $log_entry_prefix . "След garbage collection: " . round($memory_after_gc_mb, 2) . "MB\n";
        }

        // Стъпка 4: Синхронизиране на продуктите с пакетна обработка за оптимизация на паметта
        $sync_result_stats = [];

        // Проверяваме дали имаме много продукти и ги обработваме на порции
        // Динамично определяне на batch size според използваната памет
        $memory_current = memory_get_usage(true);
        $memory_current_mb = $memory_current / 1024 / 1024;

        if ($memory_current_mb > ($memory_limit_mb * 0.6)) {
            $batch_size = 20; // Много малки порции при висока употреба на памет
        } elseif ($memory_current_mb > ($memory_limit_mb * 0.4)) {
            $batch_size = 30; // Средни порции при умерена употреба на памет
        } else {
            $batch_size = 50; // Нормални порции при ниска употреба на памет
        }

        $total_products = count($opencart_products_batch);
        $current_log .= $log_entry_prefix . "Текуща памет: " . round($memory_current_mb, 2) . "MB, Избран batch size: {$batch_size}\n";

        if ($total_products > $batch_size) {
            $current_log .= $log_entry_prefix . "Обработка на {$total_products} продукта на порции от {$batch_size} за оптимизация на паметта.\n";

            // Проверяваме паметта преди array_chunk за да предотвратим memory exhaustion
            $memory_before_chunk = memory_get_usage(true);
            $memory_before_mb = $memory_before_chunk / 1024 / 1024;
            $estimated_chunk_memory = ($memory_before_mb * 1.5); // Приблизително 50% увеличение за array_chunk

            if ($estimated_chunk_memory > ($memory_limit_mb * 0.7)) {
                // Ако array_chunk може да причини memory exhaustion, използваме manual chunking
                $current_log .= $log_entry_prefix . "ПРЕДУПРЕЖДЕНИЕ: array_chunk може да изчерпи паметта. Използваме manual chunking.\n";

                $batch_count = ceil($total_products / $batch_size);

                for ($batch_index = 0; $batch_index < $batch_count; $batch_index++) {
                    $start_index = $batch_index * $batch_size;
                    $batch_products = array_slice($opencart_products_batch, $start_index, $batch_size);

                    $current_log .= $log_entry_prefix . "Обработка на порция " . ($batch_index + 1) . "/{$batch_count} с " . count($batch_products) . " продукта.\n";

                    $batch_sync_stats = $this->_syncProducts($batch_products, $mfsc_id, $c_key, $current_log, $log_entry_prefix);

                    // Натрупваме статистиките
                    if (empty($sync_result_stats)) {
                        $sync_result_stats = $batch_sync_stats;
                    } else {
                        $sync_result_stats['added'] = ($sync_result_stats['added'] ?? 0) + ($batch_sync_stats['added'] ?? 0);
                        $sync_result_stats['updated'] = ($sync_result_stats['updated'] ?? 0) + ($batch_sync_stats['updated'] ?? 0);
                        $sync_result_stats['skipped'] = ($sync_result_stats['skipped'] ?? 0) + ($batch_sync_stats['skipped'] ?? 0);
                        $sync_result_stats['errors'] = ($sync_result_stats['errors'] ?? 0) + ($batch_sync_stats['errors'] ?? 0);

                        if (!empty($batch_sync_stats['message'])) {
                            $sync_result_stats['message'] = trim(($sync_result_stats['message'] ?? '') . '; ' . $batch_sync_stats['message']);
                        }
                        if (!empty($batch_sync_stats['error_message'])) {
                            $sync_result_stats['error_message'] = trim(($sync_result_stats['error_message'] ?? '') . '; ' . $batch_sync_stats['error_message']);
                        }
                    }

                    // Освобождаваме паметта за текущата порция
                    unset($batch_products, $batch_sync_stats);

                    // Принудително освобождаване на паметта
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }

                // Освобождаваме оригиналния масив
                unset($opencart_products_batch);

            } else {
                // Безопасно използване на array_chunk
                $current_log .= $log_entry_prefix . "Използваме array_chunk за разделяне на продуктите.\n";

                $product_batches = array_chunk($opencart_products_batch, $batch_size);
                $batch_count = count($product_batches);

                // Освобождаваме оригиналния масив
                unset($opencart_products_batch);

                foreach ($product_batches as $batch_index => $batch_products) {
                    $current_log .= $log_entry_prefix . "Обработка на порция " . ($batch_index + 1) . "/{$batch_count} с " . count($batch_products) . " продукта.\n";

                    $batch_sync_stats = $this->_syncProducts($batch_products, $mfsc_id, $c_key, $current_log, $log_entry_prefix);

                    // Натрупваме статистиките
                    if (empty($sync_result_stats)) {
                        $sync_result_stats = $batch_sync_stats;
                    } else {
                        $sync_result_stats['added'] = ($sync_result_stats['added'] ?? 0) + ($batch_sync_stats['added'] ?? 0);
                        $sync_result_stats['updated'] = ($sync_result_stats['updated'] ?? 0) + ($batch_sync_stats['updated'] ?? 0);
                        $sync_result_stats['skipped'] = ($sync_result_stats['skipped'] ?? 0) + ($batch_sync_stats['skipped'] ?? 0);
                        $sync_result_stats['errors'] = ($sync_result_stats['errors'] ?? 0) + ($batch_sync_stats['errors'] ?? 0);

                        if (!empty($batch_sync_stats['message'])) {
                            $sync_result_stats['message'] = trim(($sync_result_stats['message'] ?? '') . '; ' . $batch_sync_stats['message']);
                        }
                        if (!empty($batch_sync_stats['error_message'])) {
                            $sync_result_stats['error_message'] = trim(($sync_result_stats['error_message'] ?? '') . '; ' . $batch_sync_stats['error_message']);
                        }
                    }

                    // Освобождаваме паметта за текущата порция
                    unset($batch_products, $batch_sync_stats);

                    // Принудително освобождаване на паметта
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }

                unset($product_batches);
            }
        } else {
            $sync_result_stats = $this->_syncProducts($opencart_products_batch, $mfsc_id, $c_key, $current_log, $log_entry_prefix);
        }

        // Натрупване на числовите статистики
        $sync_stats['added']   += (isset($sync_result_stats['added']) ? (int)$sync_result_stats['added'] : 0);
        $sync_stats['updated'] += (isset($sync_result_stats['updated']) ? (int)$sync_result_stats['updated'] : 0);
        $sync_stats['skipped'] += (isset($sync_result_stats['skipped']) ? (int)$sync_result_stats['skipped'] : 0);
        $sync_stats['errors']  += (isset($sync_result_stats['errors']) ? (int)$sync_result_stats['errors'] : 0);

        // Обединяване на информационните съобщения
        if (!empty($sync_result_stats['message'])) {
            $sync_stats['message'] = trim($sync_stats['message'] . ($sync_stats['message'] ? '; ' : '') . $sync_result_stats['message']);
        }

        // Обединяване на съобщенията за грешки
        if (!empty($sync_result_stats['error_message'])) {
            if (!empty($sync_stats['error_message'])) {
                $sync_stats['error_message'] .= '; ' . $sync_result_stats['error_message'];
            } else {
                $sync_stats['error_message'] = $sync_result_stats['error_message'];
            }
        }

        // Финален отчет за паметта
        $memory_end = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        $memory_used_mb = ($memory_end - $memory_start) / 1024 / 1024;
        $memory_peak_mb = $memory_peak / 1024 / 1024;

        $current_log .= $log_entry_prefix . "Финален отчет за памет - Използвана: " . round($memory_used_mb, 2) . "MB, Пик: " . round($memory_peak_mb, 2) . "MB\n";

        // Предупреждение ако сме близо до лимита
        if ($memory_peak_mb > ($memory_limit_mb * 0.8)) {
            $current_log .= $log_entry_prefix . "ПРЕДУПРЕЖДЕНИЕ: Използваната памет достигна " . round(($memory_peak_mb / $memory_limit_mb) * 100, 1) . "% от лимита!\n";
        }

        return $sync_stats;
    }

    /**
     * Парсира memory limit стринга и връща стойността в байтове
     * @param string $memory_limit
     * @return int
     */
    private function _parseMemoryLimit($memory_limit) {
        if ($memory_limit == '-1') {
            return PHP_INT_MAX;
        }

        $memory_limit = trim($memory_limit);
        $last_char = strtolower($memory_limit[strlen($memory_limit) - 1]);
        $value = (int)$memory_limit;

        switch ($last_char) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    protected function addToLog($message){
        $this->current_log .= print_r($message, true) . PHP_EOL;
    }

    public function getCurrentLog(){
        return $this->current_log;
    }

    public function getCachedSyncDataFilePath(){
        return DIR_CACHE . 'eoffice_feed_data.xml';
    }

    private function requestCachedSyncData(){
        $cached_data_file = $this->getCachedSyncDataFilePath();
        if(file_exists($cached_data_file)){
            $filemtime = filemtime($cached_data_file);
            // Проверка дали файлът е по-стар от 24 часа (24 * 60 * 60 секунди)
            if(time() - $filemtime < 86400) {
                // Проверяваме размера на файла преди да го заредим
                $file_size = filesize($cached_data_file);
                $file_size_mb = $file_size / 1024 / 1024;

                // Ако файлът е по-голям от 50MB, логваме предупреждение
                if ($file_size_mb > 50) {
                    error_log("eOffice Connector: Зареждане на голям кеширан файл ({$file_size_mb}MB): {$cached_data_file}");
                }

                // Използваме по-ефективно четене за големи файлове
                if ($file_size_mb > 100) {
                    // За много големи файлове използваме stream reading
                    $handle = fopen($cached_data_file, 'r');
                    if ($handle) {
                        $content = stream_get_contents($handle);
                        fclose($handle);
                        return $content;
                    }
                    return false;
                } else {
                    return file_get_contents($cached_data_file);
                }
            }
        }
        return false;
    }

    private function setCachedSyncData($raw_data){
        $cached_data_file = $this->getCachedSyncDataFilePath();
        file_put_contents($cached_data_file, $raw_data);

        // Изтриваме кеша с обработени продукти при записване на нови XML данни
        $this->_clearCachedProducts();
    }

    private function _requestAndPrepareData($c_key, &$current_log, &$sync_stats, $log_entry_prefix) {
        $raw_data = $this->requestCachedSyncData();
        if(!$raw_data){
            $raw_data = $this->requestSyncData();
            $this->setCachedSyncData($raw_data);
        }
        $connection_ok = false;
        $conversion_error = false;
        $supplier_data_source = null;
        $data_is_xml = false;

        if (!$raw_data) {
            $current_log .= $log_entry_prefix . "Грешка: Неуспешно извличане на данни за {$c_key}.\n";
            $sync_stats['error_message'] = "Неуспешно извличане на данни";
            return [$supplier_data_source, $data_is_xml, $connection_ok, $conversion_error];
        }

        $connection_ok = true;
        $current_log .= $log_entry_prefix . "Данните са извлечени успешно за {$c_key}.\n";

        // Определяме дали данните са XML на базата на getConnectionType(), което е по-надеждно
        // отколкото да гадаем по съдържанието, ако типът на връзката е ясно дефиниран.
        $connection_type_string = $this->getConnectionType(); // getConnectionType() връща HTML, трябва да се внимава
        // Безопасна проверка дали 'XML' се съдържа в низа, върнат от getConnectionType()
        if (is_string($connection_type_string) && stripos($connection_type_string, 'XML') !== false) {
            $data_is_xml = true;
        }

        if ($data_is_xml) {
            $supplier_data_source = $raw_data;
            $current_log .= $log_entry_prefix . "XML данните са получени и ще бъдат обработени поточно за {$c_key}.\n";
        } else {
            $decoded_json = json_decode($raw_data, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $supplier_data_source = $decoded_json;
                $current_log .= $log_entry_prefix . "Данните (JSON) са конвертирани в масив за {$c_key}.\n";
            } else {
                if (is_array($raw_data)) { // Ако requestSyncData() по някаква причина върне масив
                    $supplier_data_source = $raw_data;
                    $current_log .= $log_entry_prefix . "Данните са получени като масив за {$c_key}.\n";
                } else {
                    $current_log .= $log_entry_prefix . "Предупреждение: Данните за {$c_key} не са XML и не са валиден JSON. Може да има проблем с обработката.\n";
                    $sync_stats['error_message'] = "Неподдържан формат на данните или грешка при конверсия от JSON.";
                    // Не маркираме $conversion_error = true тук, защото processSupplierFeed ще се опита да го обработи
                    // и ако $supplier_data_source не е масив, ще влезе в else клона и ще върне грешка.
                    // Ако $raw_data все пак е стринг (не XML, не JSON), $supplier_data_source ще е null и ще се хване от проверката за empty.
                    $supplier_data_source = $raw_data; // Запазваме суровите данни, ако не са JSON, за евентуален друг тип обработка
                }
            }
        }
        return [$supplier_data_source, $data_is_xml, $connection_ok, $conversion_error];
    }

    /**
     * Синхронизира партида от OpenCart продукти с базата данни.
     * @param array $opencart_products_batch Батч от продукти за синхронизация.
     * @param int $mfsc_id ID на настройката за синхронизация.
     * @param string $c_key Ключ на конектора.
     * @param string &$current_log Текущия лог.
     * @param string $log_entry_prefix Префикс за лог записите.
     * @return array Статистика за синхронизацията.
     */
    private function _syncProducts($opencart_products_batch, $mfsc_id, $c_key, &$current_log, $log_entry_prefix) {
        if ($this->mfs_model_instance) {
             if (!is_object($this->mfs_model_instance)) {
                 $current_log .= $log_entry_prefix . "КРИТИЧНА ГРЕШКА: Основният модел 'model_extension_module_multi_feed_syncer' не успя да се зареди или не е обект в _syncProducts.\n";
                 return ['added' => 0, 'updated' => 0, 'skipped' => 0, 'errors' => count($opencart_products_batch), 'error_message' => 'Основният модел multi_feed_syncer не можа да бъде зареден или не е обект.'];
             } elseif (!is_callable([$this->mfs_model_instance, 'doSync'])) {
                 $current_log .= $log_entry_prefix . "КРИТИЧНА ГРЕШКА: Методът 'doSync' не съществува в заредения модел 'model_extension_module_multi_feed_syncer'.\n";
                 return ['added' => 0, 'updated' => 0, 'skipped' => 0, 'errors' => count($opencart_products_batch), 'error_message' => "Методът 'doSync' не е намерен в основния модел."];
             }
        }

        $sync_stats = $this->mfs_model_instance->doSync($opencart_products_batch, $mfsc_id);
        $current_log .= $log_entry_prefix . "doSync е завършен за {$c_key}. Статистика: " . print_r($sync_stats, true) . "\n";
        return $sync_stats;
    }

    /**
     * Рекурсивно конвертира DOMNode обект в PHP масив.
     *
     * @param DOMNode $node DOMNode за конвертиране.
     * @return array|string Конвертираният масив или стринг, ако е текстов възел.
     */
    // private function _convertDomNodeToArray(DOMNode $node) {
    //     $output = [];
    //     switch ($node->nodeType) {
    //         case XML_CDATA_SECTION_NODE:
    //         case XML_TEXT_NODE:
    //             // Запазваме оригиналния текст без trim за да не загубим специални символи
    //             $text_content = $node->textContent;
    //             // Декодираме HTML entities за да запазим специални символи като кавички
    //             $output = html_entity_decode($text_content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    //             // Правим trim само ако няма специални символи в началото/края
    //             if (!preg_match('/^[\s]*["\']|["\'][\s]*$/', $output)) {
    //                 $output = trim($output);
    //             }
    //             break;

    //         case XML_ELEMENT_NODE:
    //             for ($i = 0, $m = $node->childNodes->length; $i < $m; $i++) {
    //                 $child = $node->childNodes->item($i);
    //                 $v = $this->_convertDomNodeToArray($child);
    //                 if (isset($child->tagName)) {
    //                     $t = $child->tagName;
    //                     if (!isset($output[$t])) {
    //                         $output[$t] = [];
    //                     }
    //                     $output[$t][] = $v;
    //                 } elseif ($v !== '' && $v !== null) {
    //                      $output[] = $v; // Previously $output = $v; which could be problematic if $v is an array and $output is already an array.
    //                 }
    //             }

    //             if ($node->attributes->length && !empty($output)) { // If $output is not empty, it means it's an array from child elements
    //                 $attributes = [];
    //                 foreach ($node->attributes as $attrName => $attrNode) {
    //                     $attributes[$attrName] = (string)$attrNode->value;
    //                 }
    //                 if (!is_array($output)) { // Should not happen if there were child elements that formed an array
    //                     $output = ['#text' => $output]; // If $output was a string from a single text node child before attributes.
    //                 }
    //                 $output['@attributes'] = $attributes;
    //             } elseif ($node->attributes->length) { // If $output is empty (no child elements or only text content handled by XML_TEXT_NODE)
    //                  $output = []; // Ensure $output is an array to hold attributes
    //                  foreach ($node->attributes as $attrName => $attrNode) {
    //                     $output['@attributes'][$attrName] = (string)$attrNode->value;
    //                 }
    //             }

    //             // Simplify arrays with single elements, but not for @attributes
    //             if (is_array($output)) {
    //                 foreach ($output as $t => $v) {
    //                     if (is_array($v) && count($v) == 1 && $t != '@attributes') {
    //                          // Check if the single element is keyed by 0 (numeric array from multiple same-name children)
    //                         if (array_key_exists(0, $v)) {
    //                             $output[$t] = $v[0];
    //                         }
    //                     }
    //                 }
    //                 // If after all processing, $output is an empty array, and there were no attributes and no real child nodes (e.g. <element></element>)
    //                 // it should represent an empty string, not an empty array.
    //                 if (empty($output) && !$node->attributes->length && !$node->hasChildNodes()) {
    //                     $output = '';
    //                 } elseif (empty($output) && $node->attributes->length && !$node->hasChildNodes()){
    //                     // This case is handled by the attributes logic above, $output will contain ['@attributes' => ...]
    //                 }
    //             }
    //             break;
    //     }
        
    //     // Final check for elements that are truly empty (e.g. <tag/> or <tag></tag> without attributes)
    //     // and should be represented as empty string rather than empty array.
    //     if (is_array($output) && empty($output) && $node->nodeType == XML_ELEMENT_NODE && !$node->attributes->length) {
    //         $hasRealText = false;
    //         if ($node->hasChildNodes()) {
    //             for ($i=0; $i < $node->childNodes->length; $i++) {
    //                 $childNode = $node->childNodes->item($i);
    //                 // Check for non-empty text nodes or CDATA sections
    //                 if (($childNode->nodeType == XML_TEXT_NODE || $childNode->nodeType == XML_CDATA_SECTION_NODE) && trim($childNode->textContent) !== '') {
    //                     $hasRealText = true;
    //                     break;
    //                 }
    //                 // If there's any other element node, it's not "empty" in terms of structure
    //                 if ($childNode->nodeType == XML_ELEMENT_NODE) {
    //                     $hasRealText = true; // Treat as not empty structure
    //                     break;
    //                 }
    //             }
    //         }
    //         if (!$hasRealText) {
    //             return '';
    //         }
    //     }

    //     return $output;
    // }


    /**
     * Рекурсивно конвертира DOMNode обект в PHP масив с оптимизация за памет.
     *
     * @param DOMNode $node DOMNode за конвертиране.
     * @return array|string Конвертираният масив или стринг, ако е текстов възел.
     */
    private function _convertDomNodeToArray(DOMNode $node) {
        $output = [];
        switch ($node->nodeType) {
            case XML_CDATA_SECTION_NODE:
            case XML_TEXT_NODE:
                $output = trim($node->textContent);
                break;

            case XML_ELEMENT_NODE:
                $child_nodes_length = $node->childNodes->length;
                for ($i = 0; $i < $child_nodes_length; $i++) {
                    $child = $node->childNodes->item($i);
                    $v = $this->_convertDomNodeToArray($child);
                    if (isset($child->tagName)) {
                        $t = $child->tagName;
                        if (!isset($output[$t])) {
                            $output[$t] = [];
                        }
                        $output[$t][] = $v;
                    } elseif ($v !== '' && $v !== null) {
                         $output[] = $v;
                    }
                    // Освобождаваме референцията към child възела
                    unset($child, $v);
                }

                // Обработка на атрибути
                $attributes_length = $node->attributes->length;
                if ($attributes_length && !empty($output)) {
                    $attributes = [];
                    foreach ($node->attributes as $attrName => $attrNode) {
                        $attributes[$attrName] = (string)$attrNode->value;
                    }
                    if (!is_array($output)) {
                        $output = ['#text' => $output];
                    }
                    $output['@attributes'] = $attributes;
                    unset($attributes); // Освобождаваме временния масив
                } elseif ($attributes_length) {
                     $output = [];
                     foreach ($node->attributes as $attrName => $attrNode) {
                        $output['@attributes'][$attrName] = (string)$attrNode->value;
                    }
                }

                // Опростяване на масиви с един елемент
                if (is_array($output)) {
                    foreach ($output as $t => $v) {
                        if (is_array($v) && count($v) == 1 && $t != '@attributes') {
                            if (array_key_exists(0, $v)) {
                                $output[$t] = $v[0];
                            }
                        }
                    }
                    // Проверка за празни елементи
                    if (empty($output) && !$attributes_length && !$node->hasChildNodes()) {
                        $output = '';
                    }
                }
                break;
        }
        
        // Final check for elements that are truly empty (e.g. <tag/> or <tag></tag> without attributes)
        // and should be represented as empty string rather than empty array.
        if (is_array($output) && empty($output) && $node->nodeType == XML_ELEMENT_NODE && !$node->attributes->length) {
            $hasRealText = false;
            if ($node->hasChildNodes()) {
                for ($i=0; $i < $node->childNodes->length; $i++) {
                    $childNode = $node->childNodes->item($i);
                    // Check for non-empty text nodes or CDATA sections
                    if (($childNode->nodeType == XML_TEXT_NODE || $childNode->nodeType == XML_CDATA_SECTION_NODE) && trim($childNode->textContent) !== '') {
                        $hasRealText = true;
                        break;
                    }
                    // If there's any other element node, it's not "empty" in terms of structure
                    if ($childNode->nodeType == XML_ELEMENT_NODE) {
                        $hasRealText = true; // Treat as not empty structure
                        break;
                    }
                }
            }
            if (!$hasRealText) {
                return '';
            }
        }

        return $output;
    }


    /**
     * Връща данни за конкретен лог запис, които да се покажат в таб "Логове".
     * Главният контролер ще извика този метод, подавайки десериализираните `process_data` от лога.
     * @param array $log_process_data Десериализирани данни от колоната `multi_feed_syncer_logs.process_data`.
     * @return string Форматиран низ или структурирани данни за показване.
     */
    public function getSyncLogData(array $log_process_data) {
        $info_parts = [];
        if (isset($log_process_data['connection_established'])) {
            $info_parts[] = "Връзка установена: " . ($log_process_data['connection_established'] ? 'Да' : 'Не');
        }
        // 'received' не се ползва директно в $sync_stats, по-скоро броя мапирани продукти е релевантен
        // if (isset($log_process_data['received'])) {
        //    $info_parts[] = "Получени: " . (int)$log_process_data['received'];
        // }
        if (isset($log_process_data['added'])) {
            $info_parts[] = "Добавени: " . (int)$log_process_data['added'];
        }
        if (isset($log_process_data['updated'])) {
            $info_parts[] = "Актуализирани: " . (int)$log_process_data['updated'];
        }
        if (isset($log_process_data['skipped'])) {
            $info_parts[] = "Пропуснати: " . (int)$log_process_data['skipped'];
        }
        if (isset($log_process_data['errors'])) {
            $info_parts[] = "Грешки: " . (int)$log_process_data['errors'];
        }
        if (!empty($log_process_data['message'])) {
            $info_parts[] = "Съобщение: " . htmlspecialchars($log_process_data['message']);
        }
         if (!empty($log_process_data['error_message'])) {
            $info_parts[] = "Съобщение за грешка: " . htmlspecialchars($log_process_data['error_message']);
        }
        return implode(', <br/>' . PHP_EOL, $info_parts);
    }

    public function getCategoryMappings() {
        return [
            'a modo mio' => 'Кетъринг > Кухненско оборудване > Кафе машини',
            '%dolce gusto%' => 'Кетъринг > Кухненско оборудване > Кафе машини',
            '%krups дребна електродомакинска техника%' => 'Кетъринг > Кухненско оборудване > Кафе машини',
            '%напитки, кетъринг > вода, мляко, сокове, безалкохолни напитки > мляко%' => 'Кетъринг > Вода, Мляко, Сокове, Безалкохолни напитки',
            '%напитки, кетъринг > кетъринг консумативи > дървени чаши, прибори%' => 'Кетъринг > Кетъринг консумативи > Дървени прибори',
            '%напитки, кетъринг > кетъринг консумативи%' => 'Кетъринг > Кетъринг консумативи',
            '%напитки, кетъринг > уреди за дома%' => 'Кетъринг > Кухненско оборудване',
            '%напитки, кетъринг%' => 'Кетъринг',

            '%монитори%' => 'TV, Монитори, Видео и Аудио',
            '%monitors%' => 'TV, Монитори, Видео и Аудио',
            '%телевизори > аксесоари%' => 'TV, Монитори, Видео и Аудио',
            '%телевизори%' => 'TV, Монитори, Видео и Аудио',
            '%benq zowie продукти > monitors%' => 'TV, Монитори, Видео и Аудио',
            '%мултимедийни проектори%' => 'TV, Монитори, Видео и Аудио',
            '%аудио%' => 'TV, Монитори, Видео и Аудио',

            '%настолни компютри%' => 'Лаптопи и компютри',
            '%офис техника > лаптопи%' => 'Лаптопи и компютри > Лаптопи',
             '%сървъри%' => 'Лаптопи и компютри',

            '%детска коледа%' => 'Книжарница > За детето',
            '%консумативи, хартии, офис техника > консумативи за офис техника > съвместими консумативи > консумативи panasonic%' => 'Книжарница > Консумативи за офис техника > Съвместими консумативи > Panasonic',
            '%консумативи, хартии, офис техника > консумативи за офис техника > съвместими консумативи > консумативи pantum%' => 'Книжарница > Консумативи за офис техника > Съвместими консумативи',     
            '%консумативи за офис техника > съвместими консумативи%' => 'Книжарница > Консумативи за офис техника > Съвместими консумативи',
            'консумативи за офис техника > оригинални консумативи' => 'Книжарница > Консумативи за офис техника > Оригинални консумативи',
            '%консумативи, хартии, офис техника%' => 'Книжарница > Консумативи за офис техника',
            'консумативи, хартии, офис техника' => 'Книжарница > Консумативи за офис техника',
            '%поддръжка на офиса > компютри, лаптопи, таблети%' => 'Книжарница > Поддръжка на офиса',

            '%употребявана техника > мултифункционални устройства%' => 'PC  компоненти и периферия > Печатаща техника',
            'консумативи, хартии, офис техника > принтери и мултифункционални устройства > лазерни принтери' => 'PC  компоненти и периферия > Печатаща техника > Лазерни принтери',
            'консумативи, хартии, офис техника > принтери и мултифункционални устройства > лазерни мултифункционални устройства' => 'PC  компоненти и периферия > Печатаща техника > Лазерни МФУ',
            'консумативи, хартии, офис техника > принтери и мултифункционални устройства > мастилено-струйни мултифункционални устройства' => 'PC  компоненти и периферия > Печатаща техника > Мастиленоструйни МФУ',
            'консумативи, хартии, офис техника > принтери и мултифункционални устройства' => 'PC  компоненти и периферия > Печатаща техника',
            '%benq zowie продукти > mouse pad%' => 'PC  компоненти и периферия > Компютърна периферия > Мишки',
            '%benq zowie продукти > mouse%' => 'PC  компоненти и периферия > Компютърна периферия > Мишки',
            '%benq zowie продукти%' => 'PC  компоненти и периферия > Компютърна периферия',
            '%компютърна периферия > аксесоари%' => 'PC  компоненти и периферия > Компютърна периферия > Аксесоари',
            '%компютърна периферия > accessories%' => 'PC  компоненти и периферия > Компютърна периферия > Аксесоари',
            '%компютърна периферия > keyboards%' => 'PC  компоненти и периферия > Компютърна периферия > Клавиатури',
            '%компютърна периферия > клавиатури%' => 'PC  компоненти и периферия > Компютърна периферия > Клавиатури',
            '%компютърна периферия > мишки%' => 'PC  компоненти и периферия > Компютърна периферия > Мишки',
            '%компютърна периферия > mouse%' => 'PC  компоненти и периферия > Компютърна периферия > Мишки',
            '%компютърна периферия > слушалки%' => 'PC  компоненти и периферия > Компютърна периферия > Слушалки',
             '%мрежови продукти%' => 'PC  компоненти и периферия > Компютърна периферия',
            '%мрежово и инструменти%' => 'PC  компоненти и периферия > Компютърна периферия',
            '%компютърна периферия%' => 'PC  компоненти и периферия > Компютърна периферия',
            '%компоненти за компютри%' => 'PC  компоненти и периферия > Компоненти за компютри',
            '%офис техника%' => 'PC  компоненти и периферия',
            'офис техника' => 'PC  компоненти и периферия',
            '%storage > външни ssd%' => 'PC  компоненти и периферия > Информационни носители > Твърди дискови устройства',
            '%storage > вътрешни ssd%' => 'PC  компоненти и периферия > Информационни носители > Твърди дискови устройства',
            '%storage%' => 'PC  компоненти и периферия > Информационни носители',

            

            '%трапезни / бар столове/ кухненски дивани%' => 'Мебели > Трапезни / бар столове',
            '%мебели и обзавеждане%' => 'Мебели',
            
            '%телефони, таблети, часовници, е-книги, аксесоари%' => 'Мобилни устройства',
        ];
    }

    /**
     * Връща пътя до кеширания файл с обработени продукти
     * @return string
     */
    private function _getCachedProductsFilePath() {
        $storage_dir = DIR_STORAGE . 'multi_feed_syncer/' . $this->connector_key . '/';
        return $storage_dir . 'products_data.serialized';
    }

    /**
     * Зарежда кеширани обработени продукти
     * @return array|false Масив с продукти или false ако няма кеширани данни
     */
    private function _loadCachedProducts() {
        $cache_file = $this->_getCachedProductsFilePath();

        if (!file_exists($cache_file)) {
            return false;
        }

        try {
            $serialized_data = file_get_contents($cache_file);
            if ($serialized_data === false) {
                return false;
            }

            $products_data = unserialize($serialized_data);
            if ($products_data === false || !is_array($products_data)) {
                // Повреден кеш файл, изтриваме го
                unlink($cache_file);
                return false;
            }

            return $products_data;
        } catch (Exception $e) {
            // При грешка изтриваме повредения файл
            if (file_exists($cache_file)) {
                unlink($cache_file);
            }
            return false;
        }
    }

    /**
     * Записва обработени продукти в кеша
     * @param array $products_data Масив с обработени продукти
     * @return bool True при успех, false при грешка
     */
    private function _saveCachedProducts($products_data) {
        if (!is_array($products_data) || empty($products_data)) {
            return false;
        }

        $cache_file = $this->_getCachedProductsFilePath();
        $dir = dirname($cache_file);

        // Създаваме директорията, ако не съществува
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        try {
            $serialized_data = serialize($products_data);
            return file_put_contents($cache_file, $serialized_data) !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Изтрива кеширания файл с обработени продукти
     * @return bool True при успех или ако файлът не съществува, false при грешка
     */
    private function _clearCachedProducts() {
        $cache_file = $this->_getCachedProductsFilePath();

        if (!file_exists($cache_file)) {
            return true; // Файлът не съществува, считаме за успех
        }

        try {
            return unlink($cache_file);
        } catch (Exception $e) {
            return false;
        }
    }
}
?>