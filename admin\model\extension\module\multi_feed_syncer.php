<?php
class ModelExtensionModuleMultiFeedSyncer extends Model {

    // --- Начало: Константи за пакетна обработка ---
    /**
     * Размер на порцията за пакетно вмъкване на продукти в oc_product и свързаните таблици.
     * По-малките стойности намаляват риска от препълване на паметта и твърде големи SQL заявки,
     * но могат да увеличат общото време за изпълнение поради повече на брой заявки.
     */
    const MFS_BATCH_INSERT_CHUNK_SIZE = 50; // Може да се настрои според нуждите (напр. 50-100)

    /**
     * Размер на порцията за пакетно актуализиране на продукти (цена/количество) с CASE WHEN.
     * Тази операция е по-ефективна, затова може да позволи по-големи порции.
     */
    const MFS_BATCH_UPDATE_CHUNK_SIZE = 150; // Може да се настрои (напр. 100-200)

    /**
     * Време за изчакване (в секунди) между обработката на отделните порции.
     * Полезно за намаляване на натоварването на сървъра при много големи синхронизации.
     * Стойност 0 означава без изчакване.
     */
    const MFS_SLEEP_BETWEEN_CHUNKS = 1; // Променете на 1 или повече, ако е необходимо
    // --- Край: Константи за пакетна обработка ---

    /**
     * Кеш за категории - масив с ключ пълния път и стойност category_id
     * @var array
     */
    private static $categories_cache = [];

    /**
     * Кеш за parent-child връзки на категории - масив с ключ category_id и стойност parent_id
     * @var array
     */
    private static $categories_parent_cache = [];

    // OCFilter интеграцията е премахната

    /**
     * Кеш за стандартни OpenCart атрибути и групи
     * @var array
     */
    private $attribute_groups_cache = [];
    private $attributes_cache = [];

    /**
     * Предварително заредени атрибути и групи за оптимизация
     * @var array
     */
    private $preloaded_attribute_groups = [];
    private $preloaded_attributes = [];
    private $attributes_preloaded = false;

    private $log; // Уверете се, че $this->log се инициализира коректно в конструктора на модела или където е подходящо
    private $test_mode = false;
    private $current_dummy_product_id_counter = 100000; // Започваме от по-високо число за уникалност
    private $current_dummy_general_id_counter = 700000;

    public function __construct($registry) {
        parent::__construct($registry);

        ini_set('memory_limit', '512M');

        $this->log = new Log('multi_feed_syncer.log');

        // OCFilter интеграцията е премахната

        // Инициализация на кеш променливите за стандартни атрибути
        $this->attribute_groups_cache = [];
        $this->attributes_cache = [];

        // Инициализация на предварително заредените данни
        $this->preloaded_attribute_groups = [];
        $this->preloaded_attributes = [];
        $this->attributes_preloaded = false;
    }

    public function install() {
        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_logs` (
                `mfs_id` INT AUTO_INCREMENT,
                `mfsc_id` INT,
                `connection_status` TINYINT(1),
                `process_timing` INT,
                `process_data` TEXT,
                `process_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`mfs_id`),
                INDEX `mfsc_id` (`mfsc_id`),
                INDEX `connection_status` (`connection_status`),
                INDEX `process_timing` (`process_timing`),
                INDEX `process_date` (`process_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ", false);

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_connectors` (
                `mfsc_id` INT AUTO_INCREMENT,
                `connector` VARCHAR(32),
                `connector_key` VARCHAR(32) UNIQUE,
                `markup_percentage` DECIMAL(5,2) DEFAULT 0.00,
                PRIMARY KEY (`mfsc_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ", false);

        $query = $this->_executeQuery("SHOW COLUMNS FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` LIKE 'markup_percentage'", false);
        if (!$query->num_rows) {
            $this->_executeQuery("ALTER TABLE `" . DB_PREFIX . "multi_feed_syncer_connectors` ADD `markup_percentage` DECIMAL(5,2) DEFAULT 0.00 AFTER `connector_key`", false);
        }

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "product_image_download_queue` (
              `queue_id` int(11) NOT NULL AUTO_INCREMENT,
              `mfsc_id` int(11) NOT NULL,
              `product_id` int(11) NOT NULL,
              `image_url` TEXT NOT NULL,
              `is_main_image` tinyint(1) NOT NULL DEFAULT '0',
              `sort_order` int(3) NOT NULL DEFAULT '0',
              `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
              `attempts` tinyint(1) NOT NULL DEFAULT '0',
              `date_added` datetime NOT NULL,
              PRIMARY KEY (`queue_id`),
              UNIQUE KEY `product_image_url` (`product_id`, `image_url`(255)),
              KEY `status_attempts` (`status`, `attempts`),
              KEY `mfsc_id` (`mfsc_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ", false);

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` (
              `mapping_id` int(11) NOT NULL AUTO_INCREMENT,
              `mfsc_id` int(11) NOT NULL,
              `source_category_path` TEXT NOT NULL,
              `target_category_path` TEXT NOT NULL,
              `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`mapping_id`),
              KEY `mfsc_id` (`mfsc_id`),
              KEY `source_category_path` (`source_category_path`(255))
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ", false);

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_product_to_connector` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `product_id` int(11) NOT NULL,
              `mfsc_id` int(11) NOT NULL,
              `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `product_connector` (`product_id`, `mfsc_id`),
              KEY `mfsc_id` (`mfsc_id`),
              KEY `product_id` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ", false);
    }

    public function uninstall() {
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_logs`;", false);
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_connectors`;", false);
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "product_image_download_queue`;", false);
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`;", false);
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_product_to_connector`;", false);
    }

    public function getActivatedConnectors() {
        $query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` ORDER BY `connector` ASC", false);
        return $query->rows;
    }

    public function addConnector($data) {
        $this->_executeQuery("INSERT INTO " . DB_PREFIX . "multi_feed_syncer_connectors SET connector = '" . $this->db->escape($data['connector']) . "', connector_key = '" . $this->db->escape($data['connector_key']) . "'", false);
        return $this->db->getLastId();
    }
    
    public function getConnector($mfsc_id) {
        $query = $this->_executeQuery("SELECT * FROM " . DB_PREFIX . "multi_feed_syncer_connectors WHERE mfsc_id = '" . (int)$mfsc_id . "'", false  );
        return $query->row;
    }

    public function getConnectorById($mfsc_id) {
        return $this->getConnector($mfsc_id);
    }

    /**
     * Взема данни за конектор по неговия ключ (connector_key).
     *
     * @param string $connector_key Ключът на конектора.
     * @return array|false Връща асоциативен масив с данните за конектора или false, ако не е намерен.
     */
    public function getConnectorByKey($connector_key) {
        $query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` WHERE `connector_key` = '" . $this->db->escape($connector_key) . "'", false);
        if ($query->num_rows) {
            return $query->row;
        } else {
            return false;
        }
    }

    public function getLogs($data = array()) {
        $sql = "SELECT mfs.*, mfc.connector FROM `" . DB_PREFIX . "multi_feed_syncer_logs` mfs LEFT JOIN `" . DB_PREFIX . "multi_feed_syncer_connectors` mfc ON (mfs.mfsc_id = mfc.mfsc_id)";
        
        $sql .= " ORDER BY mfs.process_date DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 10; // По подразбиране 10 лога на страница
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->_executeQuery($sql, false);
        return $query->rows;
    }

    public function getTotalLogs() {
        $query = $this->_executeQuery("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "multi_feed_syncer_logs`", false);
        return $query->row['total'];
    }
    
    public function addLog($log_data) {
        $this->_executeQuery("INSERT INTO " . DB_PREFIX . "multi_feed_syncer_logs SET 
            mfsc_id = '" . (int)$log_data['mfsc_id'] . "', 
            connection_status = '" . (int)$log_data['connection_status'] . "', 
            process_timing = '" . (int)$log_data['process_timing'] . "', 
            process_data = '" . $this->db->escape(isset($log_data['process_data']) ? serialize($log_data['process_data']) : '') . "', 
            process_date = NOW()", false);
        return $this->db->getLastId();
    }

    public function updateMarkupPercentage($mfsc_id, $markup) {
        $this->_executeQuery("UPDATE " . DB_PREFIX . "multi_feed_syncer_connectors SET markup_percentage = '" . (float)$markup . "' WHERE mfsc_id = '" . (int)$mfsc_id . "'", false);
    }

    /**
     * Обработва синхронизацията на продуктовите данни.
     * Този метод ще бъде описан подробно по-късно. [cite: 46]
     */
    public function setTestMode(bool $mode) {
        $this->test_mode = $mode;
        if ($this->test_mode) {
            $this->log->write("MultiFeed Syncer: ТЕСТОВ РЕЖИМ АКТИВИРАН.");
        } else {
            $this->log->write("MultiFeed Syncer: ТЕСТОВ РЕЖИМ ДЕАКТИВИРАН.");
        }
    }

    public function getTestMode() {
        return $this->test_mode;
    }

    /**
     * Добавя запис в лога за синхронизациите.
     * Използва полетата от таблицата oc_multi_feed_syncer_logs.
     *
     * @param int $mfsc_id ID на конектора
     * @param array $sync_stats Асоциативен масив със статистиките от синхронизацията
     * @param int $execution_time Време за изпълнение в секунди
     */
    public function addSynchronizationLog($mfsc_id, $sync_stats, $execution_time) {
        // Определяне на connection_status.
        // За момента, ако има грешки, приемаме, че connection_status е 0 (неуспех/проблем), иначе 1 (успех).
        $connection_status_val = (isset($sync_stats['errors']) && $sync_stats['errors'] > 0) ? 0 : 1;

        $sql = "INSERT INTO `" . DB_PREFIX . "multi_feed_syncer_logs` SET ";
        $sql .= "`mfsc_id` = " . (int)$mfsc_id . ", ";
        $sql .= "`connection_status` = " . (int)$connection_status_val . ", ";
        $sql .= "`process_timing` = " . (int)$execution_time . ", ";
        // Използваме json_encode за process_data, тъй като е по-стандартно и лесно за разчитане от serialize
        $sql .= "`process_data` = '" . $this->db->escape(json_encode($sync_stats)) . "', ";
        $sql .= "`process_date` = NOW()";
        
        // Използваме _executeQuery, за да сме съвместими с тестовия режим
        $this->_executeQuery($sql, false);
    }


    public function _executeQuery(string $sql, bool $do_log = true, $log_file=null) {
        $trimmed_sql = trim($sql);
        // Извличане на първата дума от SQL заявката, за да се определи типът ѝ
        $sql_command_first_word = strtoupper(substr($trimmed_sql, 0, strpos($trimmed_sql, ' ') ?: strlen($trimmed_sql)));

        if ($this->test_mode) {
            // В тестов режим НЕ изпълняваме заявки, които променят данни (INSERT, UPDATE, DELETE, CREATE, ALTER, DROP)
            if (in_array($sql_command_first_word, ['INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP'])) {
                // if (isset($this->log)) { // Проверка дали $this->log е наличен
                if ($do_log)    
                    $this->writeToCronLog("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION): " . $sql, $log_file);
                // }
                // Връщаме обект stdClass с num_rows = 0 и row = [], за да симулираме резултат от заявка без грешка, но без данни.
                // За INSERT може да се наложи да симулираме getLastId() по друг начин.
                $mock_result = new stdClass();
                $mock_result->num_rows = 0;
                $mock_result->row = [];
                $mock_result->rows = [];
                return $mock_result; // Връщаме обект, за да избегнем грешки при опит за достъп до num_rows
            } else if (in_array($sql_command_first_word, ['SELECT', 'SHOW'])) {
                // SELECT и SHOW заявките се изпълняват, но се логват.
                if ($do_log) $this->writeToCronLog("MultiFeed Syncer (Test Mode SQL - EXECUTED): " . $sql, $log_file);
                return $this->db->query($sql);
            } else {
                // За други непознати типове заявки
                if ($do_log) $this->writeToCronLog("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION - Unhandled Type '" . $sql_command_first_word . "'): " . $sql, $log_file);
                
                $mock_result = new stdClass(); // Подобно на горното
                $mock_result->num_rows = 0;
                $mock_result->row = [];
                $mock_result->rows = [];
                return $mock_result;
            }
        }
        else {
            if ($do_log) $this->writeToCronLog("MultiFeed Syncer (SQL): " . $sql, $log_file);
        }
        
        // Ако не сме в тестов режим, изпълняваме всички заявки нормално
        return $this->db->query($sql);
    }

    private function _getLastId() {
        if ($this->test_mode) {
            // Тъй като INSERT заявките не се изпълняват реално, db->getLastId() няма да върне смислена стойност.
            // Генерираме уникално dummy ID.
            $dummy_id = $this->current_dummy_general_id_counter++;
            // $this->writeToCronLog("MultiFeed Syncer (Test Mode): _getLastId() called, returning dummy ID: " . $dummy_id);
            return $dummy_id;
        }
        return $this->db->getLastId();
    }

    // Статични променливи за кеширане на данни между извиквания на класово ниво
    private static $existing_seo_keywords = null;
    private static $categories_cache_loaded = false;
    private static $attributes_cache_loaded = false;

    // Глобален кеш за съответствията на категории по конектор
    private static $categories_mapping_cache = [];
    private static $categories_mapping_loaded = false;

    // Глобален кеш за продукти без категории
    private static $products_without_categories_cache = [];
    private static $products_without_categories_loaded = false;

    // Глобален кеш за съществуващи атрибути на продукти
    private static $global_existing_product_attributes = [];
    private static $global_product_attributes_loaded = false;

    // КРИТИЧНА ОПТИМИЗАЦИЯ: Кеш за атрибути създадени в текущата сесия
    private static $session_created_attributes = [];

    // КРИТИЧНА ОПТИМИЗАЦИЯ: Опростен кеш само по име и език (без group_id)
    private $simplified_attributes_cache = [];

    // Система за проследяване на синхронизирани продукти
    private static $current_sync_product_ids = [];
    private static $product_tracking_enabled = false;

    public function doSync($opencart_product_data, $mfsc_id) {

        //$this->setTestMode(true);

        $start_time = microtime(true);

        // Инициализираме проследяването на продукти
        $this->_initializeProductTracking($mfsc_id);

        // $this->log = new Log('multi_feed_syncer_process.log'); // По-специфичен лог за процеса

        $processed_info = [
            'added' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'received' => count($opencart_product_data),
            'standard_attributes' => 0,
            'log_details' => [],
            'total_time_seconds' => 0
        ];

        if(defined('SYNC_TEST_MODE')) { // todo: remove
            return $processed_info;
        }

        // $one_product = reset($opencart_product_data);
        // $this->writeToCronLog("MultiFeed Syncer: First product: " . print_r($one_product, true));

        // return $processed_info;

        if (empty($opencart_product_data)) {
            $processed_info['log_details'][] = "Няма продукти за синхронизиране.";
            $this->addSynchronizationLog($mfsc_id, $processed_info, 0);
            return $processed_info;
        }

        if($this->test_mode) {
            $this->writeToCronLog("MultiFeed Syncer (Test Mode): Начало на синхронизацията за конектор ID: {$mfsc_id}.");
        } else {
            $this->writeToCronLog("MultiFeed Syncer: Начало на синхронизацията за конектор ID: {$mfsc_id}.");
        }

        // $this->writeToCronLog("MultiFeed Syncer: Продукти за обновяване: " . count($opencart_product_data));


        $default_language_id = (int)$this->config->get('config_language_id') ? (int)$this->config->get('config_language_id') : 1;
        $config_language = $this->config->get('config_language') ? $this->config->get('config_language') : 'bg';

        // $this->load->model('localisation/language');
        // $languages = $this->model_localisation_language->getLanguages();
        if (empty($languages)) { // Резервен вариант, ако не успее да зареди езиците
            $languages = [['language_id' => $default_language_id, 'code' => $config_language]];
        }

        // --- Начало: Оптимизирано зареждане на съществуващи SEO keywords ---
        if (self::$existing_seo_keywords === null) {
            self::$existing_seo_keywords = [];
            $query_keywords = $this->_executeQuery("SELECT LOWER(keyword) as keyword FROM `" . DB_PREFIX . "seo_url`", false, 'mfs_seo_url.log');
            if ($query_keywords && $query_keywords->rows) {
                foreach ($query_keywords->rows as $row) {
                    self::$existing_seo_keywords[$row['keyword']] = true;
                }
            }
            // $this->writeToCronLog("MultiFeed Syncer: Заредени " . count(self::$existing_seo_keywords) . " съществуващи SEO keywords в статичния кеш.", 'mfs_seo_url.log');
        } else {
            // $this->writeToCronLog("MultiFeed Syncer: Използване на кеширани SEO keywords (" . count(self::$existing_seo_keywords) . " записа).", 'mfs_seo_url.log');
        }
        // --- Край: Оптимизирано зареждане на съществуващи SEO keywords ---

        // --- Начало: Оптимизирано зареждане на кеш за категории ---
        if (!self::$categories_cache_loaded) {
            $this->_loadCategoriesCache();
            self::$categories_cache_loaded = true;
            // $this->writeToCronLog("MultiFeed Syncer: Категории заредени в статичния кеш.", 'mfs_categories.log');
        } else {
            // $this->writeToCronLog("MultiFeed Syncer: Използване на кеширани категории (" . count(self::$categories_cache) . " записа).", 'mfs_categories.log');
        }
        // --- Край: Оптимизирано зареждане на кеш за категории ---

        // --- Начало: Оптимизирано зареждане на кеш за атрибути ---
        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        if (!self::$attributes_cache_loaded) {
            $this->_preloadAllAttributes($language_id);
            self::$attributes_cache_loaded = true;
            // $this->writeToCronLog("MultiFeed Syncer: Атрибути заредени в статичния кеш.", 'mfs_attributes.log');
        } else {
            $cached_count = isset($this->preloaded_attributes) ? count($this->preloaded_attributes) : 0;
            // $this->writeToCronLog("MultiFeed Syncer: Използване на кеширани атрибути ({$cached_count} записа).", 'mfs_attributes.log');
        }
        // --- Край: Оптимизирано зареждане на кеш за атрибути ---

        // --- Начало: Оптимизирано зареждане на кеш за съответствия на категории ---
        if (!self::$categories_mapping_loaded) {
            $this->_preloadCategoriesMapping($mfsc_id);
            self::$categories_mapping_loaded = true;
            // $this->writeToCronLog("MultiFeed Syncer: Съответствия на категории заредени в статичния кеш.", 'mfs_categories.log');
        } else {
            $cached_count = isset(self::$categories_mapping_cache[$mfsc_id]) ? count(self::$categories_mapping_cache[$mfsc_id]) : 0;
            // $this->writeToCronLog("MultiFeed Syncer: Използване на кеширани съответствия на категории ({$cached_count} записа).", 'mfs_categories.log');
        }
        // --- Край: Оптимизирано зареждане на кеш за съответствия на категории ---

        // Предварително зареждане на всички съществуващи атрибути на продукти
        $this->_preloadGlobalExistingProductAttributes($language_id);

        // Използваме оригиналните цени без надценка
        $markup_percentage = 0.0;

        $identifier_field = 'sku'; 

        $product_codes_to_check = [];
        $valid_products_for_processing = []; 
 
        foreach ($opencart_product_data as $product_key => $product_data) {
            if (empty($product_data[$identifier_field])) {
                $processed_info['skipped']++;
                $processed_info['log_details'][] = "Продукт (#{$product_key}) е пропуснат поради липсващ идентификатор ('{$identifier_field}').";
                continue;
            }
            $sku = $product_data[$identifier_field];
            $product_codes_to_check[] = $this->db->escape($sku);
            
            // Вземане на името по подразбиране, за да се използва при генериране на SEO URL за съществуващи продукти
            $default_lang_name = 'N/A'; // Стойност по подразбиране, ако името не може да бъде намерено
            if (isset($product_data['product_description'][$default_language_id]['name']) && !empty(trim($product_data['product_description'][$default_language_id]['name'])) ) {
                $default_lang_name = $product_data['product_description'][$default_language_id]['name'];
            } elseif (isset($product_data['name']) && !empty(trim($product_data['name']))) { // Резервен вариант
                $default_lang_name = $product_data['name'];
            }
            $product_data['default_language_name'] = $default_lang_name;

            $valid_products_for_processing[$sku] = $product_data; 
        }
        unset($opencart_product_data); // Освобождаваме паметта от големия входен масив
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        $existing_products_map = [];
        if (!empty($product_codes_to_check)) {
            // Разделяне на заявката за съществуващи продукти на порции, ако е нужно
            $check_codes_chunks = array_chunk($product_codes_to_check, 500); // Порция от 500 SKU
            foreach ($check_codes_chunks as $chunk_of_codes) {
                if (empty($chunk_of_codes)) continue;
                $query_codes_string = "'" . implode("','", $chunk_of_codes) . "'";
                $query = $this->_executeQuery("SELECT `{$identifier_field}`, `product_id` FROM `" . DB_PREFIX . "product` WHERE `{$identifier_field}` IN (" . $query_codes_string . ")", false, 'mfs_products.log');
                if($query && $query->rows){
                    foreach ($query->rows as $row) {
                        $existing_products_map[$row[$identifier_field]] = $row['product_id'];
                    }
                }
            }
        }
        unset($product_codes_to_check, $check_codes_chunks); 

        $products_to_add = [];
        $products_to_update = []; 

        foreach ($valid_products_for_processing as $product_code => $product_data) {
            if (isset($existing_products_map[$product_code])) {
                $product_id = $existing_products_map[$product_code];
                $product_data['product_id'] = $product_id; 
                $products_to_update[$product_id] = $this->_filterProductDataForUpdate($product_data, $identifier_field);
            } else {
                $products_to_add[$product_code] = $product_data; 
            }
        }
        unset($valid_products_for_processing, $existing_products_map);
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        // --- Начало: Оптимизирано зареждане на кеш за продукти без категории ---
        $product_ids_for_category_check = array_keys($products_to_update);
        if (!empty($product_ids_for_category_check)) {
            $this->_preloadProductsWithoutCategories($product_ids_for_category_check);
            // $this->writeToCronLog("MultiFeed Syncer: Заредени продукти без категории в глобалния кеш.", 'mfs_categories.log');
        }
        // --- Край: Оптимизирано зареждане на кеш за продукти без категории ---



        // $this->writeToCronLog("DEBUG: products_to_add = " . print_r($products_to_add, true));

        if (!empty($products_to_add)) {
            // Предаваме self::$existing_seo_keywords по референция
            $this->_processProductsToInsert($products_to_add, $processed_info, $languages, $default_language_id, $markup_percentage, $identifier_field, self::$existing_seo_keywords, $mfsc_id);
        }
        unset($products_to_add); 
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        // if(!empty($products_to_update)) {
        //     if(array_key_exists( '78815', $products_to_update)) {
        //         $products_to_update = [
        //             '78815' => $products_to_update['78815']
        //         ];
        //     }
        //     else {
        //         $first_product = reset($products_to_update);
        //         $products_to_update = [
        //             $first_product['product_id'] => $first_product
        //         ];
        //     }
        // }


        // $this->writeToCronLog("DEBUG: products_to_update = " . print_r($products_to_update, true));


        if (!empty($products_to_update)) {
            // Предаваме self::$existing_seo_keywords по референция
            $this->_processProductsToUpdate($products_to_update, $processed_info, $markup_percentage, $identifier_field, self::$existing_seo_keywords, $mfsc_id);
        }
        unset($products_to_update); 
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        $end_time = microtime(true);
        $processed_info['total_time_seconds'] = round($end_time - $start_time, 2);
        $processed_info['log_details'][] = "Синхронизацията приключи за {$processed_info['total_time_seconds']} секунди.";

        // Финализираме проследяването на продукти
        $this->_finalizeProductTracking($mfsc_id, $processed_info);

        $this->addSynchronizationLog($mfsc_id, $processed_info, $processed_info['total_time_seconds']);

        // DEBUG: Показваме финалната статистика
        $this->writeToCronLog("DEBUG STATS FINAL: processed_info = " . print_r($processed_info, true));

        $final_summary = "MultiFeed Syncer: Край на синхронизацията за конектор ID {$mfsc_id}. ";
        $final_summary .= "Получени: {$processed_info['received']}, Добавени: {$processed_info['added']}, Актуализирани: {$processed_info['updated']}, Пропуснати: {$processed_info['skipped']}, Грешки: {$processed_info['errors']}. ";
        $final_summary .= "Време: {$processed_info['total_time_seconds']} сек.";
        $this->writeToCronLog($final_summary);

        // define('SYNC_TEST_MODE', true);

        return $processed_info;
    }


    /**
     * Взема порция от задачи от опашката и ги маркира като 'processing', за да не ги вземе друг процес.
     * @param int $limit
     * @return array
     */
    public function getAndLockPendingImages($limit = 100) {

        // Първо, избираме ID-тата на задачите, които ще обработваме

        $sql = "SELECT `queue_id` FROM `" . DB_PREFIX . "product_image_download_queue` WHERE `status` = 'pending' AND `attempts` < 5 ORDER BY `queue_id` ASC LIMIT " . (int)$limit;

        $query = $this->_executeQuery($sql, true, 'mfs_images.log');

        // file_put_contents(DIR_LOGS . 'multi_feed_syncer_image_downloader.log', 'getAndLockPendingImages SQL: ' . $sql, FILE_APPEND);
        
        if (!$query->num_rows) {
            return [];
        }

        // file_put_contents(DIR_LOGS . 'multi_feed_syncer_image_downloader.log', 'getAndLockPendingImages: ' . print_r($query->rows, true), FILE_APPEND);
        
        $task_ids = array_column($query->rows, 'queue_id');
        $id_string = implode(',', $task_ids);

        // Заключваме тези редове, като сменяме статуса им
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = 'processing' WHERE `queue_id` IN (" . $id_string . ")", true, 'mfs_images.log');
        
        // Сега извличаме пълните данни за заключените задачи
        $full_task_query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "product_image_download_queue` WHERE `queue_id` IN (" . $id_string . ")", true, 'mfs_images.log');

        return $full_task_query->rows;
    }

    /**
     * Актуализира продуктите с новите пътища на изтеглените изображения.
     * @param array $processed_tasks Масив от задачи, съдържащи new_local_path.
     */
    public function updateProductsWithDownloadedImages(array $processed_tasks) {
        if (empty($processed_tasks)) {
            return;
        }
        
        $main_image_cases = [];
        $additional_images_by_product = [];

        foreach ($processed_tasks as $task) {
            if ($task['is_main_image']) {
                $main_image_cases[] = "WHEN " . (int)$task['product_id'] . " THEN '" . $this->db->escape($task['new_local_path']) . "'";
            } else {
                $additional_images_by_product[(int)$task['product_id']][] = [
                    'image' => $task['new_local_path'],
                    'sort_order' => $task['sort_order']
                ];
            }
        }

        // Пакетна актуализация на основното изображение в oc_product
        if (!empty($main_image_cases)) {
            $product_ids = array_unique(array_column($processed_tasks, 'product_id'));
            $sql_update_main = "UPDATE `" . DB_PREFIX . "product` SET `image` = (CASE `product_id` " . implode(" ", $main_image_cases) . " ELSE `image` END) WHERE `product_id` IN (" . implode(',', $product_ids) . ")";
            $this->_executeQuery($sql_update_main, true, 'mfs_images.log');
        }

        // Добавяне на допълнителните изображения
        if (!empty($additional_images_by_product)) {

            // 1. Вземаме ID-тата на продуктите, чиито допълнителни изображения ще бъдат заменени
            $product_ids_to_clear_images = array_keys($additional_images_by_product);

            // 2. Изтриваме ВСИЧКИ СТАРИ допълнителни изображения за тези продукти
            
            $this->_executeQuery("DELETE FROM `" . DB_PREFIX . "product_image` WHERE `product_id` IN (" . implode(',', $product_ids_to_clear_images) . ")", true, 'mfs_images.log');
        

            // 3. Подготвяме и вмъкваме новите
            $image_values = [];
            foreach ($additional_images_by_product as $product_id => $images) {
                foreach ($images as $img_data) {
                    $image_values[] = "(" . (int)$product_id . ", '" . $this->db->escape($img_data['image']) . "', " . (int)$img_data['sort_order'] . ")";
                }
            }

            if (!empty($image_values)) {
                // Вече можем да използваме чист INSERT, тъй като сме изтрили старите записи
                $sql_insert_additional = "INSERT INTO `" . DB_PREFIX . "product_image` (`product_id`, `image`, `sort_order`) VALUES " . implode(", ", $image_values);
                $this->_executeQuery($sql_insert_additional, true, 'mfs_images.log');
            }

        }
    }

    /**
     * Променя статуса на група задачи в опашката.
     * @param array $task_ids
     * @param string $status
     */
    public function updateQueueTasks(array $task_ids, string $status) {
        if (empty($task_ids)) {
            return;
        }
        $valid_statuses = ['completed', 'pending', 'failed'];
        if (!in_array($status, $valid_statuses)) {
            return;
        }
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = '" . $status . "' WHERE `queue_id` IN (" . implode(',', $task_ids) . ")");
    }

    /**
     * Увеличава броя опити за неуспешни задачи.
     * @param array $task_ids
     */
    public function incrementQueueTaskAttempts(array $task_ids) {
        if (empty($task_ids)) {
            return;
        }
        // Задачите се маркират като 'failed' и се увеличава броя опити.
        // Cron-ът ще ги пропусне, ако attempts >= 5.
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = 'failed', `attempts` = `attempts` + 1 WHERE `queue_id` IN (" . implode(',', $task_ids) . ")", true, 'mfs_images.log');
    }

    /**
     * Взема порция от failed задачи от опашката за повторно опитване.
     * @param int $limit Максимален брой задачи за обработка
     * @param int $max_attempts Максимален брой опити (по подразбиране 5)
     * @param int $retry_after_hours След колко часа да се опита отново (по подразбиране 24)
     * @return array
     */
    public function getAndLockFailedImages($limit = 50, $max_attempts = 5, $retry_after_hours = 24) {
        // Взема failed изображения, които не са достигнали максималния брой опити
        // и са били неуспешни преди повече от определения период
        $sql = "SELECT `queue_id` FROM `" . DB_PREFIX . "product_image_download_queue`
                WHERE `status` = 'failed'
                AND `attempts` < " . (int)$max_attempts . "
                AND `date_added` < DATE_SUB(NOW(), INTERVAL " . (int)$retry_after_hours . " HOUR)
                ORDER BY `attempts` ASC, `queue_id` ASC
                LIMIT " . (int)$limit;

        $query = $this->_executeQuery($sql, true, 'mfs_images.log');

        if (!$query->num_rows) {
            return [];
        }

        $task_ids = array_column($query->rows, 'queue_id');
        $id_string = implode(',', $task_ids);

        // Заключваме тези редове, като сменяме статуса им на 'processing'
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = 'processing' WHERE `queue_id` IN (" . $id_string . ")", true, 'mfs_images.log');

        // Извличаме пълните данни за заключените задачи
        $full_task_query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "product_image_download_queue` WHERE `queue_id` IN (" . $id_string . ")", true, 'mfs_images.log');

        return $full_task_query->rows;
    }

    /**
     * Рестартира failed изображения, като ги връща в pending статус.
     * @param array $task_ids Масив от queue_id за рестартиране
     * @return bool
     */
    public function retryFailedImages(array $task_ids) {
        if (empty($task_ids)) {
            return false;
        }

        $id_string = implode(',', array_map('intval', $task_ids));
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue`
                             SET `status` = 'pending', `date_added` = NOW()
                             WHERE `queue_id` IN (" . $id_string . ")
                             AND `status` = 'failed'", true, 'mfs_images.log');
        return true;
    }


    private function _processProductsToInsert(array $products_to_add_map, array &$processed_info, array $languages, int $default_language_id, float $markup_percentage, string $identifier_field, array &$existing_seo_keywords, int $mfsc_id) {
        $num_initially_to_add = count($products_to_add_map);
        $processed_info['log_details'][] = "Подготвени {$num_initially_to_add} продукта за пакетно добавяне.";

        try {
            // Предаваме $existing_seo_keywords
            $successfully_added_count_total = $this->_batchInsertProducts($products_to_add_map, $languages, $default_language_id, $markup_percentage, $identifier_field, $processed_info, $existing_seo_keywords, $mfsc_id);
            
            $processed_info['added'] += $successfully_added_count_total;

            if ($successfully_added_count_total < $num_initially_to_add) {
                $failed_count = $num_initially_to_add - $successfully_added_count_total;
                // Грешките се отчитат вътре в _batchInsertProducts, тук само добавяме обобщено съобщение
                $processed_info['log_details'][] = "{$failed_count} продукта не можаха да бъдат напълно добавени по време на _batchInsertProducts (вижте детайлните логове от метода).";
            }

        } catch (Exception $e) {
            $processed_info['errors'] += $num_initially_to_add; 
            $processed_info['log_details'][] = "Критична грешка по време на _processProductsToInsert (извън _batchInsertProducts): " . $e->getMessage();
        }
    }

    private function _processProductsToUpdate(array $products_to_update_map, array &$processed_info, float $markup_percentage, string $identifier_field, array &$existing_seo_keywords, int $mfsc_id) {
        $num_to_update = count($products_to_update_map);
        $processed_info['log_details'][] = "Подготвени {$num_to_update} продукта за пакетно актуализиране.";

        // $this->writeToCronLog("Подготвени {$num_to_update} продукта за пакетно актуализиране.", 'mfs_products.log');

        try {
            // Предаваме $processed_info и $existing_seo_keywords
            $updated_count = $this->_batchUpdateProducts($products_to_update_map, $markup_percentage, $identifier_field, $processed_info, $existing_seo_keywords, $mfsc_id);
            $processed_info['updated'] += $updated_count;
            // Грешките и детайлите се управляват вътре в _batchUpdateProducts
        } catch (Exception $e) {
            $processed_info['errors'] += $num_to_update;
            $processed_info['log_details'][] = "Критична грешка по време на _processProductsToUpdate: " . $e->getMessage();
        }
    }
        
    private function _filterProductDataForUpdate(array $product_data, string $identifier_field) {
        
        $filtered = [
            'product_id' => $product_data['product_id'],
            'sku' => $product_data['sku']
        ];
        if (array_key_exists('price', $product_data)) {
            $filtered['price'] = $product_data['price'];
        }
        if (array_key_exists('quantity', $product_data)) {
            $filtered['quantity'] = $product_data['quantity'];
        }
        if (array_key_exists('image', $product_data)) {
            $filtered['image'] = $product_data['image'];
        }
        if (array_key_exists('product_image', $product_data)) {
            $filtered['product_image'] = $product_data['product_image'];
        }
        if (array_key_exists('categories_data_source', $product_data)) {
            $filtered['categories_data_source'] = $product_data['categories_data_source'];
        }
        if (array_key_exists('attributes_data_source', $product_data)) {
            $filtered['attributes_data_source'] = $product_data['attributes_data_source'];
        }
        if (array_key_exists('processed_attributes', $product_data)) {
            $filtered['processed_attributes'] = $product_data['processed_attributes'];
        }
        if (array_key_exists('manufacturer_id', $product_data)) {
            $filtered['manufacturer_id'] = $product_data['manufacturer_id'];
        }
        if (array_key_exists('manufacturer_name_source', $product_data)) {
            $filtered['manufacturer_name_source'] = $product_data['manufacturer_name_source'];
        }

        // Включваме името на продукта от езика по подразбиране за генериране на SEO URL, ако е нужно
        if (isset($product_data['default_language_name'])) {
            $filtered['default_language_name'] = $product_data['default_language_name'];
        }

        // Включваме идентификатора за логване
        if (isset($product_data[$identifier_field])) {
            $filtered[$identifier_field] = $product_data[$identifier_field];
        } elseif (isset($product_data['sku'])) { // Резервен вариант, ако $identifier_field е различен от 'sku'
            $filtered['sku_for_log'] = $product_data['sku'];
        }
        return $filtered;
    }

    private function writeToCronLog($message, $log_file=null) {
        $log_file = $log_file ? DIR_LOGS . $log_file : DIR_LOGS . 'multi_feed_syncer.log'; // [cite: 26]
        $time = date('Y-m-d H:i:s');
        file_put_contents($log_file, $time . ': ' . $message . PHP_EOL, FILE_APPEND);
    }

    /**
     * Проследява синхронизиран продукт директно в основния модел
     * @param int $product_id ID на продукта
     * @param int $mfsc_id ID на конектора
     */
    private function _trackSyncedProductInConnector($product_id, $mfsc_id) {
        if (self::$product_tracking_enabled) {
            $this->_trackSyncedProduct($product_id);
        }
    }

    /**
     * Инициализира проследяването на синхронизирани продукти
     * @param int $mfsc_id ID на конектора
     */
    private function _initializeProductTracking($mfsc_id) {
        // Проверяваме дали конекторът поддържа проследяване на продукти
        $connector_info = $this->getConnectorById($mfsc_id);
        if (!$connector_info) {
            self::$product_tracking_enabled = false;
            return;
        }

        // Автоматично откриване на поддръжка - проверяваме дали конекторът е eOffice
        $connector_key = $connector_info['connector_key'];
        self::$product_tracking_enabled = ($connector_key === 'eoffice');

        if (self::$product_tracking_enabled) {
            self::$current_sync_product_ids = [];
            $this->writeToCronLog("MultiFeed Syncer: Инициализирано проследяване на продукти за конектор {$connector_key} (ID: {$mfsc_id})", 'mfs_product_tracking.log');
        }
    }

    /**
     * Добавя product_id към списъка на синхронизирани продукти
     * @param int $product_id ID на продукта
     */
    private function _trackSyncedProduct($product_id) {
        if (self::$product_tracking_enabled && !in_array($product_id, self::$current_sync_product_ids)) {
            self::$current_sync_product_ids[] = $product_id;
        }
    }

    /**
     * Финализира проследяването на продукти и нулира бройките на изчезналите
     * @param int $mfsc_id ID на конектора
     * @param array &$processed_info Референция към информацията за обработката
     */
    private function _finalizeProductTracking($mfsc_id, &$processed_info) {
        if (!self::$product_tracking_enabled) {
            return;
        }

        try {
            // Премахваме дублирани записи
            self::$current_sync_product_ids = array_unique(self::$current_sync_product_ids);
            $current_synced_count = count(self::$current_sync_product_ids);

            $log_message = "Проследени {$current_synced_count} продукта в текущата синхронизация.";
            $processed_info['log_details'][] = $log_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $log_message, 'mfs_product_tracking.log');

            // Зареждаме всички продукти от таблицата за текущия конектор
            $query = $this->_executeQuery("
                SELECT `product_id`
                FROM `" . DB_PREFIX . "multi_feed_syncer_product_to_connector`
                WHERE `mfsc_id` = '" . (int)$mfsc_id . "'
            ", false, 'mfs_product_tracking.log');

            $previous_synced_products = [];
            if ($query && $query->rows) {
                foreach ($query->rows as $row) {
                    $previous_synced_products[] = (int)$row['product_id'];
                }
            }

            $previous_synced_count = count($previous_synced_products);
            $log_message = "Намерени {$previous_synced_count} продукта от предишни синхронизации.";
            $processed_info['log_details'][] = $log_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $log_message, 'mfs_product_tracking.log');

            // Намираме продукти които са изчезнали от текущата синхронизация
            $disappeared_products = array_diff($previous_synced_products, self::$current_sync_product_ids);
            $disappeared_count = count($disappeared_products);

            if ($disappeared_count > 0) {
                $log_message = "Намерени {$disappeared_count} продукта които вече не участват в синхронизацията.";
                $processed_info['log_details'][] = $log_message;
                $this->writeToCronLog("MultiFeed Syncer: " . $log_message, 'mfs_product_tracking.log');

                // Нулираме бройките на изчезналите продукти
                $this->_zeroQuantityForProducts($disappeared_products, $processed_info);

                // Изтриваме записите за изчезналите продукти от таблицата
                $this->_removeProductsFromTracking($disappeared_products, $mfsc_id, $processed_info);
            } else {
                $log_message = "Няма изчезнали продукти.";
                $processed_info['log_details'][] = $log_message;
                $this->writeToCronLog("MultiFeed Syncer: " . $log_message, 'mfs_product_tracking.log');
            }

            // Актуализираме таблицата с текущо синхронизираните продукти
            $this->_updateProductTracking(self::$current_sync_product_ids, $mfsc_id, $processed_info);

        } catch (Exception $e) {
            $error_message = "ГРЕШКА при финализиране на проследяването: " . $e->getMessage();
            $processed_info['log_details'][] = $error_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $error_message, 'mfs_product_tracking.log');
        }
    }

    /**
     * Нулира бройките на продукти
     * @param array $product_ids Масив с ID-та на продукти
     * @param array &$processed_info Референция към информацията за обработката
     */
    private function _zeroQuantityForProducts($product_ids, &$processed_info) {
        if (empty($product_ids)) {
            return;
        }

        try {
            // Batch операция за нулиране на бройки
            $product_ids_string = implode(',', array_map('intval', $product_ids));

            $this->_executeQuery("
                UPDATE `" . DB_PREFIX . "product`
                SET `quantity` = 0, `date_modified` = NOW()
                WHERE `product_id` IN ({$product_ids_string})
            ", false, 'mfs_product_tracking.log');

            $affected_rows = $this->db->countAffected();
            $log_message = "Нулирани бройки на {$affected_rows} продукта.";
            $processed_info['log_details'][] = $log_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $log_message . " (IDs: " . $product_ids_string . ")", 'mfs_product_tracking.log');

        } catch (Exception $e) {
            $error_message = "ГРЕШКА при нулиране на бройки: " . $e->getMessage();
            $processed_info['log_details'][] = $error_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $error_message, 'mfs_product_tracking.log');
        }
    }

    /**
     * Премахва продукти от таблицата за проследяване
     * @param array $product_ids Масив с ID-та на продукти
     * @param int $mfsc_id ID на конектора
     * @param array &$processed_info Референция към информацията за обработката
     */
    private function _removeProductsFromTracking($product_ids, $mfsc_id, &$processed_info) {
        if (empty($product_ids)) {
            return;
        }

        try {
            $product_ids_string = implode(',', array_map('intval', $product_ids));

            $this->_executeQuery("
                DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_product_to_connector`
                WHERE `product_id` IN ({$product_ids_string})
                AND `mfsc_id` = '" . (int)$mfsc_id . "'
            ", false, 'mfs_product_tracking.log');

            $affected_rows = $this->db->countAffected();
            $log_message = "Премахнати {$affected_rows} записа от таблицата за проследяване.";
            $processed_info['log_details'][] = $log_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $log_message . " (IDs: " . $product_ids_string . ")", 'mfs_product_tracking.log');

        } catch (Exception $e) {
            $error_message = "ГРЕШКА при премахване от проследяването: " . $e->getMessage();
            $processed_info['log_details'][] = $error_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $error_message, 'mfs_product_tracking.log');
        }
    }

    /**
     * Актуализира таблицата за проследяване с текущо синхронизираните продукти
     * @param array $product_ids Масив с ID-та на продукти
     * @param int $mfsc_id ID на конектора
     * @param array &$processed_info Референция към информацията за обработката
     */
    private function _updateProductTracking($product_ids, $mfsc_id, &$processed_info) {
        if (empty($product_ids)) {
            $log_message = "Няма продукти за актуализиране в проследяването.";
            $processed_info['log_details'][] = $log_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $log_message, 'mfs_product_tracking.log');
            return;
        }

        try {
            // Batch операция за добавяне/актуализиране на записи
            $values = [];
            foreach ($product_ids as $product_id) {
                $values[] = "('" . (int)$product_id . "', '" . (int)$mfsc_id . "')";
            }

            if (!empty($values)) {
                $values_string = implode(',', $values);

                $this->_executeQuery("
                    INSERT INTO `" . DB_PREFIX . "multi_feed_syncer_product_to_connector`
                    (`product_id`, `mfsc_id`)
                    VALUES {$values_string}
                    ON DUPLICATE KEY UPDATE `date_modified` = NOW()
                ", false, 'mfs_product_tracking.log');

                $affected_rows = $this->db->countAffected();
                $log_message = "Актуализирани {$affected_rows} записа в таблицата за проследяване.";
                $processed_info['log_details'][] = $log_message;
                $this->writeToCronLog("MultiFeed Syncer: " . $log_message . " за " . count($product_ids) . " продукта", 'mfs_product_tracking.log');
            }

        } catch (Exception $e) {
            $error_message = "ГРЕШКА при актуализиране на проследяването: " . $e->getMessage();
            $processed_info['log_details'][] = $error_message;
            $this->writeToCronLog("MultiFeed Syncer: " . $error_message, 'mfs_product_tracking.log');
        }
    }

    /**
     * Изчиства всички статични кешове в doSync метода
     * Полезно при нужда от принудително презареждане на данните
     */
    public function clearStaticCaches() {
        self::$existing_seo_keywords = null;
        self::$categories_cache_loaded = false;
        self::$attributes_cache_loaded = false;
        // OCFilter кеш е премахнат

        // Изчистване на глобалния кеш за атрибути на продукти
        self::$global_existing_product_attributes = [];
        self::$global_product_attributes_loaded = false;

        $this->writeToCronLog("MultiFeed Syncer: Всички статични кешове са изчистени, включително глобалния кеш за атрибути на продукти.");
    }

    /**
     * Анализира лог файла за SQL оптимизация и предоставя препоръки
     * @return array Масив с анализ и препоръки
     */
    public function analyzeSqlPerformance() {
        $log_file = DIR_LOGS . 'multi_feed_syncer.log';
        if (!file_exists($log_file)) {
            return ['error' => 'Лог файлът не съществува'];
        }

        $log_content = file_get_contents($log_file);
        $lines = explode("\n", $log_content);

        $sql_queries = [];
        $repeated_queries = [];
        $performance_issues = [];
        $product_id_issues = [];

        foreach ($lines as $line_num => $line) {
            // Анализ на SQL заявки
            if (strpos($line, 'SQL') !== false && strpos($line, 'EXECUTED') !== false) {
                $sql_start = strpos($line, 'EXECUTED): ') + 11;
                if ($sql_start > 11) {
                    $sql_query = trim(substr($line, $sql_start));
                    $sql_normalized = preg_replace('/\s+/', ' ', $sql_query);

                    if (!isset($sql_queries[$sql_normalized])) {
                        $sql_queries[$sql_normalized] = 0;
                    }
                    $sql_queries[$sql_normalized]++;
                }
            }

            // Анализ на невалидни product_id
            if (strpos($line, 'product_id') !== false && (strpos($line, '= 0') !== false || strpos($line, "= '0'") !== false)) {
                $product_id_issues[] = [
                    'line' => $line_num + 1,
                    'content' => trim($line),
                    'type' => 'Невалиден product_id = 0'
                ];
            }
        }

        // Намираме повтарящи се заявки
        foreach ($sql_queries as $query => $count) {
            if ($count > 1) {
                $repeated_queries[$query] = $count;
            }
        }

        // Анализираме за конкретни проблеми
        foreach ($repeated_queries as $query => $count) {
            if (strpos($query, 'SELECT a.attribute_id FROM') !== false && $count > 5) {
                $performance_issues[] = [
                    'type' => 'Повтарящи се атрибутни заявки',
                    'count' => $count,
                    'recommendation' => 'Групирай всички атрибути в една заявка с IN клауза',
                    'query_sample' => substr($query, 0, 100) . '...',
                    'severity' => 'high'
                ];
            }

            if (strpos($query, 'INSERT INTO') !== false && $count > 3) {
                $performance_issues[] = [
                    'type' => 'Множество INSERT заявки',
                    'count' => $count,
                    'recommendation' => 'Използвай batch INSERT с VALUES списък',
                    'query_sample' => substr($query, 0, 100) . '...',
                    'severity' => 'medium'
                ];
            }
        }

        return [
            'total_queries' => count($sql_queries),
            'repeated_queries_count' => count($repeated_queries),
            'performance_issues' => $performance_issues,
            'product_id_issues' => $product_id_issues,
            'top_repeated' => array_slice($repeated_queries, 0, 5, true),
            'recommendations' => $this->_generateOptimizationRecommendations($performance_issues, $product_id_issues)
        ];
    }

    /**
     * Генерира конкретни препоръки за оптимизация
     * @param array $performance_issues
     * @param array $product_id_issues
     * @return array
     */
    private function _generateOptimizationRecommendations($performance_issues, $product_id_issues) {
        $recommendations = [];

        if (!empty($product_id_issues)) {
            $recommendations[] = [
                'priority' => 'КРИТИЧНО',
                'issue' => 'Невалидни product_id стойности',
                'description' => 'Открити ' . count($product_id_issues) . ' случая с product_id = 0',
                'solution' => 'Добави валидация в _queueImageForDownload() и други методи за проверка на product_id > 0',
                'code_location' => 'admin/model/extension/module/multi_feed_syncer.php'
            ];
        }

        foreach ($performance_issues as $issue) {
            if ($issue['type'] === 'Повтарящи се атрибутни заявки') {
                $recommendations[] = [
                    'priority' => 'ВИСОКО',
                    'issue' => 'Неефективни атрибутни заявки',
                    'description' => 'Открити ' . $issue['count'] . ' повтарящи се SELECT заявки за атрибути',
                    'solution' => 'Имплементирай _batchGetOrCreateAttributes() метод за групиране на заявките',
                    'estimated_improvement' => 'Намаляване на SQL заявки с ' . ($issue['count'] - 1) . ' за всеки продукт'
                ];
            }
        }

        return $recommendations;
    }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за групирано създаване/намиране на атрибути
     * Използва предварително заредените атрибути от кеша вместо SQL заявки
     * @param array $attribute_names Масив с имена на атрибути за търсене/създаване
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return array Масив [attribute_name => attribute_id]
     */
    private function _batchGetOrCreateAttributes($attribute_names, $attribute_group_id, $language_id) {
        if (empty($attribute_names)) {
            return [];
        }

        $result = [];
        $names_to_create = [];

        $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED: Започва обработка на " . count($attribute_names) . " атрибута с кеш", 'mfs_attributes.log');
        $this->writeToCronLog("MultiFeed Syncer CACHE DEBUG: Кеш съдържа " . count($this->preloaded_attributes) . " предварително заредени атрибута", 'mfs_attributes.log');

        // Стъпка 1: Проверяваме в предварително заредения кеш с нормализирани ключове
        foreach ($attribute_names as $name) {
            // ОПТИМИЗАЦИЯ: Използваме lowercase нормализирано име вместо MD5 хеш
            $normalized_name = mb_strtolower($name);
            $cache_key = $normalized_name . '_' . $attribute_group_id . '_' . $language_id;

            $this->writeToCronLog("MultiFeed Syncer CACHE DEBUG: Търсене на атрибут '{$name}' (normalized: '{$normalized_name}', group_id: {$attribute_group_id}, lang_id: {$language_id}) с ключ '{$cache_key}'", 'mfs_attributes.log');

            if (isset($this->preloaded_attributes[$cache_key])) {
                // Намерен в кеша
                $result[$name] = $this->preloaded_attributes[$cache_key];
                $this->writeToCronLog("MultiFeed Syncer CACHE HIT: Атрибут '{$name}' намерен в кеша с ID " . $this->preloaded_attributes[$cache_key], 'mfs_attributes.log');
            } else {
                // Не е намерен в кеша - проверяваме дали съществува с други group_id
                $this->_debugCacheMiss($name, $normalized_name, $attribute_group_id, $language_id);
                $names_to_create[] = $name;
                $this->writeToCronLog("MultiFeed Syncer CACHE MISS: Атрибут '{$name}' не е намерен в кеша - ще се създаде", 'mfs_attributes.log');
            }
        }

        $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED: Намерени " . count($result) . " атрибута в кеша, " . count($names_to_create) . " за създаване", 'mfs_attributes.log');

        // FALLBACK: Търсим липсващите атрибути само по име (без group_id)
        if (!empty($names_to_create)) {
            $this->writeToCronLog("MultiFeed Syncer FALLBACK SEARCH: Търсене на " . count($names_to_create) . " атрибута само по име", 'mfs_attributes.log');
            $fallback_results = $this->_findAttributesByNameOnly($names_to_create, $language_id);

            foreach ($fallback_results as $name => $data) {
                $result[$name] = $data['attribute_id'];
                // Премахваме от списъка за създаване
                $names_to_create = array_filter($names_to_create, function($n) use ($name) { return $n !== $name; });
                $this->writeToCronLog("MultiFeed Syncer FALLBACK SUCCESS: Атрибут '{$name}' намерен с fallback търсене", 'mfs_attributes.log');
            }

            $this->writeToCronLog("MultiFeed Syncer FALLBACK SEARCH: След fallback търсене остават " . count($names_to_create) . " атрибута за създаване", 'mfs_attributes.log');
        }

        // Стъпка 2: Създаваме липсващите атрибути с ИСТИНСКИ batch операции
        if (!empty($names_to_create)) {
            $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED: Създаване на " . count($names_to_create) . " нови атрибута с batch операции: " . implode(', ', $names_to_create), 'mfs_attributes.log');

            // ОПТИМИЗАЦИЯ: Batch INSERT за oc_attribute
            $attribute_values = [];
            foreach ($names_to_create as $name) {
                $attribute_values[] = "('" . (int)$attribute_group_id . "', '0')";
            }

            if (!empty($attribute_values)) {
                $sql_attributes = "INSERT INTO `" . DB_PREFIX . "attribute` (`attribute_group_id`, `sort_order`) VALUES " . implode(', ', $attribute_values);
                $this->_executeQuery($sql_attributes);
                $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED: Създадени " . count($names_to_create) . " атрибута с 1 batch INSERT заявка", 'mfs_attributes.log');

                // Получаваме ID-тата на новосъздадените атрибути
                $first_new_id = $this->_getLastId() - count($names_to_create) + 1;

                // ОПТИМИЗАЦИЯ: Batch INSERT за oc_attribute_description
                $description_values = [];
                foreach ($names_to_create as $index => $name) {
                    $attribute_id = $first_new_id + $index;
                    $description_values[] = "('" . (int)$attribute_id . "', '" . (int)$language_id . "', '" . $this->db->escape($name) . "')";
                    $result[$name] = $attribute_id;

                    // Автоматично обновяване на кеша с нормализиран ключ
                    $normalized_name = mb_strtolower($name);
                    $cache_key = $normalized_name . '_' . $attribute_group_id . '_' . $language_id;
                    $this->preloaded_attributes[$cache_key] = $attribute_id;
                    $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED UPDATE: Добавен нов атрибут '{$name}' в кеша с нормализиран ключ '{$cache_key}' и ID {$attribute_id}", 'mfs_attributes.log');
                }

                if (!empty($description_values)) {
                    $sql_descriptions = "INSERT INTO `" . DB_PREFIX . "attribute_description` (`attribute_id`, `language_id`, `name`) VALUES " . implode(', ', $description_values);
                    $this->_executeQuery($sql_descriptions, true, 'mfs_attributes.log');
                    $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED: Създадени " . count($names_to_create) . " описания на атрибути с 1 batch INSERT заявка", 'mfs_attributes.log');
                }
            }
        }

        return $result;
    }

    /**
     * КРИТИЧНО ОПТИМИЗИРАН метод за търсене на атрибути с автоматично откриване на group_id
     * Решава проблема с несъответствие между зареждане и търсене на атрибути
     * @param array $attribute_names Масив с имена на атрибути
     * @param int $default_group_id Група по подразбиране за нови атрибути
     * @param int $language_id ID на езика
     * @return array Масив [attribute_name => attribute_id]
     */
    private function _smartGetOrCreateAttributes($attribute_names, $default_group_id, $language_id) {
        if (empty($attribute_names)) {
            return [];
        }

        $result = [];
        $names_to_create = [];

        $this->writeToCronLog("MultiFeed Syncer SMART SEARCH: Започва интелигентно търсене на " . count($attribute_names) . " атрибута", 'mfs_attributes.log');

        // СТЪПКА 1: Търсим в опростения кеш (само име + език)
        foreach ($attribute_names as $name) {
            $normalized_name = mb_strtolower($name);
            $simple_cache_key = $normalized_name . '_' . $language_id;

            if (isset($this->simplified_attributes_cache[$simple_cache_key])) {
                // Намерен в опростения кеш
                $cached_data = $this->simplified_attributes_cache[$simple_cache_key];
                $result[$name] = $cached_data['attribute_id'];
                $this->writeToCronLog("MultiFeed Syncer SMART HIT: Атрибут '{$name}' намерен в опростения кеш с ID {$cached_data['attribute_id']} от група {$cached_data['group_id']}", 'mfs_attributes.log');
            } else {
                // Проверяваме дали е създаден в текущата сесия
                $session_key = $normalized_name . '_' . $language_id;
                if (isset(self::$session_created_attributes[$session_key])) {
                    $session_data = self::$session_created_attributes[$session_key];
                    $result[$name] = $session_data['attribute_id'];
                    $this->writeToCronLog("MultiFeed Syncer SESSION HIT: Атрибут '{$name}' намерен в сесийния кеш с ID {$session_data['attribute_id']}", 'mfs_attributes.log');
                } else {
                    // Не е намерен - трябва да се създаде
                    $names_to_create[] = $name;
                    $this->writeToCronLog("MultiFeed Syncer SMART MISS: Атрибут '{$name}' не е намерен - ще се създаде", 'mfs_attributes.log');
                }
            }
        }

        $this->writeToCronLog("MultiFeed Syncer SMART SEARCH: Намерени " . count($result) . " атрибута, " . count($names_to_create) . " за създаване", 'mfs_attributes.log');

        // СТЪПКА 2: Създаваме липсващите атрибути
        if (!empty($names_to_create)) {
            $new_attributes = $this->_smartCreateAttributes($names_to_create, $default_group_id, $language_id);
            $result = array_merge($result, $new_attributes);
        }

        return $result;
    }

    /**
     * КРИТИЧНО ОПТИМИЗИРАН метод за създаване на атрибути с предотвратяване на дублиране
     * @param array $attribute_names Масив с имена на атрибути за създаване
     * @param int $group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return array Масив [attribute_name => attribute_id]
     */
    private function _smartCreateAttributes($attribute_names, $group_id, $language_id) {
        if (empty($attribute_names)) {
            return [];
        }

        $result = [];
        $this->writeToCronLog("MultiFeed Syncer SMART CREATE: Започва създаване на " . count($attribute_names) . " атрибута", 'mfs_attributes.log');

        // СТЪПКА 1: Batch INSERT за oc_attribute
        $attribute_values = [];
        foreach ($attribute_names as $name) {
            $attribute_values[] = "('" . (int)$group_id . "', '0')";
        }

        if (!empty($attribute_values)) {
            $sql_attributes = "INSERT INTO `" . DB_PREFIX . "attribute` (`attribute_group_id`, `sort_order`) VALUES " . implode(', ', $attribute_values);
            $this->_executeQuery($sql_attributes);
            $this->writeToCronLog("MultiFeed Syncer SMART CREATE: Създадени " . count($attribute_names) . " атрибута с 1 batch INSERT заявка", 'mfs_attributes.log');

            // Получаваме ID-тата на новосъздадените атрибути
            $first_new_id = $this->_getLastId() - count($attribute_names) + 1;

            // СТЪПКА 2: Batch INSERT за oc_attribute_description
            $description_values = [];
            foreach ($attribute_names as $index => $name) {
                $attribute_id = $first_new_id + $index;
                $description_values[] = "('" . (int)$attribute_id . "', '" . (int)$language_id . "', '" . $this->db->escape($name) . "')";
                $result[$name] = $attribute_id;

                // КРИТИЧНО: Обновяваме всички кешове
                $this->_updateAllCachesWithNewAttribute($name, $attribute_id, $group_id, $language_id);
            }

            if (!empty($description_values)) {
                $sql_descriptions = "INSERT INTO `" . DB_PREFIX . "attribute_description` (`attribute_id`, `language_id`, `name`) VALUES " . implode(', ', $description_values);
                $this->_executeQuery($sql_descriptions, true, 'mfs_attributes.log');
                $this->writeToCronLog("MultiFeed Syncer SMART CREATE: Създадени " . count($attribute_names) . " описания на атрибути с 1 batch INSERT заявка", 'mfs_attributes.log');
            }
        }

        return $result;
    }

    /**
     * Обновява всички кешове с новосъздаден атрибут
     * @param string $name Име на атрибута
     * @param int $attribute_id ID на атрибута
     * @param int $group_id ID на групата
     * @param int $language_id ID на езика
     */
    private function _updateAllCachesWithNewAttribute($name, $attribute_id, $group_id, $language_id) {
        $normalized_name = mb_strtolower($name);

        // Обновяваме стария кеш (за съвместимост)
        $full_cache_key = $normalized_name . '_' . $group_id . '_' . $language_id;
        $this->preloaded_attributes[$full_cache_key] = $attribute_id;

        // Обновяваме опростения кеш
        $simple_cache_key = $normalized_name . '_' . $language_id;
        $this->simplified_attributes_cache[$simple_cache_key] = [
            'attribute_id' => $attribute_id,
            'group_id' => $group_id,
            'original_name' => $name
        ];

        // Обновяваме сесийния кеш
        self::$session_created_attributes[$simple_cache_key] = [
            'attribute_id' => $attribute_id,
            'group_id' => $group_id,
            'original_name' => $name
        ];

        $this->writeToCronLog("MultiFeed Syncer CACHE UPDATE: Обновени всички кешове за атрибут '{$name}' с ID {$attribute_id}", 'mfs_attributes.log');
    }

    /**
     * Алтернативен метод за търсене на атрибути само по име (без group_id)
     * Използва се като fallback когато основният метод не намира атрибута
     * @param array $attribute_names Масив с имена на атрибути
     * @param int $language_id ID на езика
     * @return array Масив [attribute_name => [attribute_id, group_id]]
     */
    private function _findAttributesByNameOnly($attribute_names, $language_id) {
        $result = [];

        foreach ($attribute_names as $name) {
            $normalized_name = mb_strtolower($name);

            // Търсим във всички заредени атрибути
            foreach ($this->preloaded_attributes as $cache_key => $attribute_id) {
                $parts = explode('_', $cache_key);
                if (count($parts) >= 3) {
                    $key_language_id = $parts[count($parts) - 1];
                    $key_group_id = $parts[count($parts) - 2];
                    $key_attribute_name = implode('_', array_slice($parts, 0, -2));

                    // Проверяваме дали името и езикът съвпадат
                    if ($key_attribute_name === $normalized_name && $key_language_id == $language_id) {
                        $result[$name] = [
                            'attribute_id' => $attribute_id,
                            'group_id' => $key_group_id
                        ];
                        $this->writeToCronLog("MultiFeed Syncer FALLBACK SEARCH: Намерен атрибут '{$name}' с ID {$attribute_id} в група {$key_group_id}", 'mfs_attributes.log');
                        break; // Вземаме първото съвпадение
                    }
                }
            }
        }

        return $result;
    }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за групирано записване на атрибути към продукти
     * Използва кеширани атрибути и проверява за съществуващи записи
     * @param array $product_attributes Масив [product_id => [attribute_name => value]]
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return int Брой записани атрибути
     */
    private function _batchInsertProductAttributesOptimized($product_attributes, $attribute_group_id, $language_id) {
        if (empty($product_attributes)) {
            return 0;
        }

        $this->writeToCronLog("Започва обработка на атрибути за " . count($product_attributes) . " продукта", 'mfs_attributes.log');

        // Събираме всички уникални имена на атрибути
        $all_attribute_names = [];
        foreach ($product_attributes as $product_id => $attributes) {
            $all_attribute_names = array_merge($all_attribute_names, array_keys($attributes));
        }
        $all_attribute_names = array_unique($all_attribute_names);

        $this->writeToCronLog("Намерени " . count($all_attribute_names) . " уникални атрибута", 'mfs_attributes.log');

        // КРИТИЧНО ОПТИМИЗАЦИЯ: Използваме новия интелигентен метод
        $attribute_ids_map = $this->_smartGetOrCreateAttributes($all_attribute_names, $attribute_group_id, $language_id);

        // Използваме директния метод за записване с проверка за съществуващи атрибути
        return $this->_directBatchInsertProductAttributes($product_attributes, $attribute_ids_map, $language_id);
    }

    /**
     * СУПЕР ОПТИМИЗИРАНО записване на атрибути към продукти с проверка за съществуващи
     * Използва вече получени attribute_ids_map и проверява за дублиращи се атрибути
     * @param array $product_attributes Масив [product_id => [attribute_name => value]]
     * @param array $attribute_ids_map Масив [attribute_name => attribute_id]
     * @param int $language_id ID на езика
     * @return int Брой записани атрибути
     */
    private function _directBatchInsertProductAttributes($product_attributes, $attribute_ids_map, $language_id) {
        if (empty($product_attributes) || empty($attribute_ids_map)) {
            return 0;
        }

        $this->writeToCronLog("Започва проверка за съществуващи атрибути в " . count($product_attributes) . " продукта с ГЛОБАЛЕН КЕШ", 'mfs_attributes.log');

        // Стъпка 1: Използваме глобалния кеш вместо SQL заявка
        $product_ids = array_keys($product_attributes);
        $existing_attributes = $this->_getExistingProductAttributesFromGlobalCache($product_ids, $language_id);

        // Стъпка 2: Подготвяме VALUES само за нови атрибути
        $insert_values = [];
        $skipped_count = 0;
        $new_count = 0;

        foreach ($product_attributes as $product_id => $attributes) {
            foreach ($attributes as $attribute_name => $value) {
                if (isset($attribute_ids_map[$attribute_name])) {
                    $attribute_id = $attribute_ids_map[$attribute_name];
                    $existing_key = $product_id . '_' . $attribute_id . '_' . $language_id;

                    // Проверяваме дали атрибутът вече съществува
                    if (isset($existing_attributes[$existing_key])) {
                        $skipped_count++;
                        $this->writeToCronLog("MultiFeed Syncer SKIP: Атрибут '{$attribute_name}' вече съществува за продукт {$product_id}", 'mfs_attributes.log');
                        continue;
                    }

                    // Добавяме само нови атрибути
                    $insert_values[] = "('" . (int)$product_id . "', '" . (int)$attribute_id . "', '" . (int)$language_id . "', '" . $this->db->escape($value) . "')";
                    $new_count++;
                }
            }
        }

        $this->writeToCronLog("MultiFeed Syncer OPTIMIZED: Подготвени $new_count нови атрибута, пропуснати $skipped_count съществуващи", 'mfs_attributes.log');

        // Стъпка 3: Изпълняваме batch INSERT само за новите атрибути
        if (!empty($insert_values)) {
            $chunks = array_chunk($insert_values, 100); // Разделяме на порции от 100
            $total_inserted = 0;

            foreach ($chunks as $chunk) {
                $sql = "INSERT IGNORE INTO `" . DB_PREFIX . "product_attribute` (product_id, attribute_id, language_id, text) VALUES " . implode(', ', $chunk);
                $this->_executeQuery($sql, true, 'mfs_attributes.log');
                $total_inserted += count($chunk);
            }

            $this->writeToCronLog("MultiFeed Syncer OPTIMIZED: Записани $total_inserted нови атрибута с batch операция", 'mfs_attributes.log');
            return $total_inserted;
        }

        $this->writeToCronLog("MultiFeed Syncer OPTIMIZED: Няма нови атрибути за записване", 'mfs_attributes.log');
        return 0;
    }

    /**
     * Предварително зарежда съществуващи атрибути за дадени продукти
     * @param array $product_ids Масив с ID-та на продукти
     * @param int $language_id ID на езика
     * @return array Масив [product_id_attribute_id_language_id => true]
     */
    private function _preloadExistingProductAttributes($product_ids, $language_id) {
        if (empty($product_ids)) {
            return [];
        }

        $existing_attributes = [];
        $product_ids_str = implode(',', array_map('intval', $product_ids));

        $this->writeToCronLog("MultiFeed Syncer PRELOAD: Зареждане на съществуващи атрибути за " . count($product_ids) . " продукта", 'mfs_attributes.log');

        $query = $this->_executeQuery("
            SELECT product_id, attribute_id, language_id
            FROM `" . DB_PREFIX . "product_attribute`
            WHERE product_id IN ($product_ids_str)
                AND language_id = '" . (int)$language_id . "'
        ", true, 'mfs_attributes.log');

        if ($query && $query->rows) {
            foreach ($query->rows as $row) {
                $key = $row['product_id'] . '_' . $row['attribute_id'] . '_' . $row['language_id'];
                $existing_attributes[$key] = true;
            }
        }

        $this->writeToCronLog("MultiFeed Syncer PRELOAD: Заредени " . count($existing_attributes) . " съществуващи атрибута", 'mfs_attributes.log');
        return $existing_attributes;
    }

    /**
     * Debug метод за анализ на cache miss - търси атрибут с различни group_id
     * @param string $original_name Оригиналното име на атрибута
     * @param string $normalized_name Нормализираното име на атрибута
     * @param int $searched_group_id Group ID с който се търси
     * @param int $language_id Language ID
     */
    private function _debugCacheMiss($original_name, $normalized_name, $searched_group_id, $language_id) {
        $this->writeToCronLog("MultiFeed Syncer CACHE MISS DEBUG: Анализ за атрибут '{$original_name}'", 'mfs_attributes.log');

        // Търсим атрибута с различни group_id в кеша
        $found_alternatives = [];
        foreach ($this->preloaded_attributes as $cache_key => $attribute_id) {
            // Проверяваме дали ключът започва с нормализираното име
            if (strpos($cache_key, $normalized_name . '_') === 0) {
                $found_alternatives[] = [
                    'cache_key' => $cache_key,
                    'attribute_id' => $attribute_id
                ];
            }
        }

        if (!empty($found_alternatives)) {
            $this->writeToCronLog("MultiFeed Syncer CACHE MISS DEBUG: Намерени алтернативи за '{$original_name}':", 'mfs_attributes.log');
            foreach ($found_alternatives as $alt) {
                $this->writeToCronLog("  - Ключ: '{$alt['cache_key']}' -> ID: {$alt['attribute_id']}", 'mfs_attributes.log');
            }

            // Анализираме разликата в group_id
            $parts = explode('_', $found_alternatives[0]['cache_key']);
            if (count($parts) >= 3) {
                $found_group_id = $parts[count($parts) - 2]; // Предпоследният елемент
                $this->writeToCronLog("MultiFeed Syncer CACHE MISS DEBUG: Атрибутът '{$original_name}' съществува с group_id {$found_group_id}, но се търси с group_id {$searched_group_id}", 'mfs_attributes.log');
            }
        } else {
            $this->writeToCronLog("MultiFeed Syncer CACHE MISS DEBUG: Атрибутът '{$original_name}' наистина не съществува в кеша", 'mfs_attributes.log');
        }

        // Показваме размера на кеша за контекст
        $cache_size = count($this->preloaded_attributes);
        $this->writeToCronLog("MultiFeed Syncer CACHE MISS DEBUG: Общо атрибути в кеша: {$cache_size}", 'mfs_attributes.log');
    }

    /**
     * ГЛОБАЛНО предварително зареждане на всички съществуващи атрибути на продукти
     * Зарежда се веднъж в началото на синхронизацията и се използва за всички продукти
     * @param int $language_id ID на езика
     * @return void
     */
    private function _preloadGlobalExistingProductAttributes($language_id) {
        // Проверяваме дали вече е заредено
        if (self::$global_product_attributes_loaded) {
            $this->writeToCronLog("MultiFeed Syncer GLOBAL PRELOAD: Глобалният кеш за атрибути на продукти вече е зареден", 'mfs_attributes.log');
            return;
        }

        $this->writeToCronLog("MultiFeed Syncer GLOBAL PRELOAD: Започва глобално зареждане на всички съществуващи атрибути на продукти", 'mfs_attributes.log');

        $start_time = microtime(true);

        // Зареждаме ВСИЧКИ съществуващи атрибути на продукти наведнъж
        $query = $this->_executeQuery("
            SELECT product_id, attribute_id, language_id
            FROM `" . DB_PREFIX . "product_attribute`
            WHERE language_id = '" . (int)$language_id . "'
        ", true, 'mfs_attributes.log');

        self::$global_existing_product_attributes = [];
        $count = 0;

        if ($query && $query->rows) {
            foreach ($query->rows as $row) {
                $key = $row['product_id'] . '_' . $row['attribute_id'] . '_' . $row['language_id'];
                self::$global_existing_product_attributes[$key] = true;
                $count++;
            }
        }

        self::$global_product_attributes_loaded = true;
        $load_time = round(microtime(true) - $start_time, 3);

        $this->writeToCronLog("MultiFeed Syncer GLOBAL PRELOAD: Заредени {$count} съществуващи атрибута на продукти за {$load_time}s", 'mfs_attributes.log');
    }

    /**
     * Получава съществуващи атрибути за продукти от глобалния кеш
     * @param array $product_ids Масив с ID-та на продукти
     * @param int $language_id ID на езика
     * @return array Масив [product_id_attribute_id_language_id => true]
     */
    private function _getExistingProductAttributesFromGlobalCache($product_ids, $language_id) {
        // Осигуряваме че глобалният кеш е зареден
        $this->_preloadGlobalExistingProductAttributes($language_id);

        $existing_attributes = [];
        $cache_hits = 0;

        foreach ($product_ids as $product_id) {
            // Търсим всички атрибути за този продукт в глобалния кеш
            foreach (self::$global_existing_product_attributes as $key => $value) {
                if (strpos($key, $product_id . '_') === 0 && strpos($key, '_' . $language_id) !== false) {
                    $existing_attributes[$key] = true;
                    $cache_hits++;
                }
            }
        }

        $this->writeToCronLog("MultiFeed Syncer GLOBAL CACHE: Намерени {$cache_hits} съществуващи атрибута за " . count($product_ids) . " продукта от глобалния кеш", 'mfs_attributes.log');
        return $existing_attributes;
    }

    /**
     * Анализира последователността на SQL операциите в лога за оптимизация
     * @return array Детайлен анализ на последователността
     */
    public function analyzeOperationSequence() {
        $log_file = DIR_LOGS . 'multi_feed_syncer.log';
        if (!file_exists($log_file)) {
            return ['error' => 'Лог файлът не съществува'];
        }

        $log_content = file_get_contents($log_file);
        $lines = explode("\n", $log_content);

        $operations = [];
        $patterns = [];
        $inefficiencies = [];

        foreach ($lines as $line_num => $line) {
            if (strpos($line, 'SQL') !== false && strpos($line, 'EXECUTED') !== false) {
                $sql_start = strpos($line, 'EXECUTED): ') + 11;
                if ($sql_start > 11) {
                    $sql_query = trim(substr($line, $sql_start));

                    // Класифицираме операцията
                    $operation_type = $this->_classifyOperation($sql_query);
                    $operations[] = [
                        'line' => $line_num + 1,
                        'type' => $operation_type,
                        'query' => $sql_query,
                        'table' => $this->_extractTableName($sql_query)
                    ];
                }
            }
        }

        // Анализираме модели и неефективности
        $patterns = $this->_findOperationPatterns($operations);
        $inefficiencies = $this->_findInefficiencies($operations);

        return [
            'total_operations' => count($operations),
            'operation_types' => $this->_countOperationTypes($operations),
            'patterns' => $patterns,
            'inefficiencies' => $inefficiencies,
            'optimization_suggestions' => $this->_generateSequenceOptimizations($patterns, $inefficiencies)
        ];
    }

    /**
     * Класифицира SQL операция по тип
     * @param string $sql_query SQL заявка
     * @return string Тип на операцията
     */
    private function _classifyOperation($sql_query) {
        $sql_upper = strtoupper(trim($sql_query));

        if (strpos($sql_upper, 'SELECT') === 0) {
            if (strpos($sql_upper, 'ATTRIBUTE') !== false) return 'SELECT_ATTRIBUTE';
            if (strpos($sql_upper, 'CATEGORY') !== false) return 'SELECT_CATEGORY';
            if (strpos($sql_upper, 'PRODUCT') !== false) return 'SELECT_PRODUCT';
            if (strpos($sql_upper, 'SEO_URL') !== false) return 'SELECT_SEO';
            return 'SELECT_OTHER';
        }

        if (strpos($sql_upper, 'INSERT') === 0) {
            if (strpos($sql_upper, 'ATTRIBUTE') !== false) return 'INSERT_ATTRIBUTE';
            if (strpos($sql_upper, 'PRODUCT') !== false) return 'INSERT_PRODUCT';
            if (strpos($sql_upper, 'SEO_URL') !== false) return 'INSERT_SEO';
            if (strpos($sql_upper, 'IMAGE_DOWNLOAD_QUEUE') !== false) return 'INSERT_IMAGE_QUEUE';
            return 'INSERT_OTHER';
        }

        if (strpos($sql_upper, 'UPDATE') === 0) return 'UPDATE';
        if (strpos($sql_upper, 'DELETE') === 0) return 'DELETE';

        return 'UNKNOWN';
    }

    /**
     * Извлича името на таблицата от SQL заявка
     * @param string $sql_query SQL заявка
     * @return string Име на таблицата
     */
    private function _extractTableName($sql_query) {
        // Опростен алгоритъм за извличане на име на таблица
        if (preg_match('/(?:FROM|INTO|UPDATE)\s+`?(\w+)`?/i', $sql_query, $matches)) {
            return $matches[1];
        }
        return 'unknown';
    }

    /**
     * Намира модели в последователността на операциите
     * @param array $operations Масив с операции
     * @return array Намерени модели
     */
    private function _findOperationPatterns($operations) {
        $patterns = [];

        // Търсим повтарящи се последователности
        for ($i = 0; $i < count($operations) - 2; $i++) {
            $sequence = [
                $operations[$i]['type'],
                $operations[$i + 1]['type'],
                $operations[$i + 2]['type']
            ];

            $pattern_key = implode(' -> ', $sequence);
            if (!isset($patterns[$pattern_key])) {
                $patterns[$pattern_key] = 0;
            }
            $patterns[$pattern_key]++;
        }

        // Връщаме само често срещаните модели
        return array_filter($patterns, function($count) { return $count > 2; });
    }

    /**
     * Намира неефективности в последователността
     * @param array $operations Масив с операции
     * @return array Намерени неефективности
     */
    private function _findInefficiencies($operations) {
        $inefficiencies = [];

        // Търсим множество SELECT заявки от същия тип подред
        $consecutive_selects = [];
        $current_type = null;
        $count = 0;

        foreach ($operations as $op) {
            if ($op['type'] === $current_type && strpos($op['type'], 'SELECT') === 0) {
                $count++;
            } else {
                if ($count > 3) {
                    $consecutive_selects[] = [
                        'type' => $current_type,
                        'count' => $count,
                        'recommendation' => 'Групирай в една заявка с IN клауза'
                    ];
                }
                $current_type = $op['type'];
                $count = 1;
            }
        }

        return $consecutive_selects;
    }

    /**
     * Брои операциите по тип
     * @param array $operations Масив с операции
     * @return array Статистика по типове
     */
    private function _countOperationTypes($operations) {
        $counts = [];
        foreach ($operations as $op) {
            if (!isset($counts[$op['type']])) {
                $counts[$op['type']] = 0;
            }
            $counts[$op['type']]++;
        }
        arsort($counts);
        return $counts;
    }

    /**
     * Генерира предложения за оптимизация на последователността
     * @param array $patterns Намерени модели
     * @param array $inefficiencies Намерени неефективности
     * @return array Предложения за оптимизация
     */
    private function _generateSequenceOptimizations($patterns, $inefficiencies) {
        $suggestions = [];

        foreach ($inefficiencies as $inefficiency) {
            if ($inefficiency['type'] === 'SELECT_ATTRIBUTE' && $inefficiency['count'] > 5) {
                $suggestions[] = [
                    'priority' => 'ВИСОКО',
                    'issue' => 'Множество последователни SELECT заявки за атрибути',
                    'current_count' => $inefficiency['count'],
                    'optimized_count' => 1,
                    'method_to_use' => '_batchGetOrCreateAttributes()',
                    'estimated_improvement' => 'Намаляване на SQL заявки с ' . ($inefficiency['count'] - 1)
                ];
            }
        }

        // Анализ на модели
        foreach ($patterns as $pattern => $count) {
            if (strpos($pattern, 'SELECT_ATTRIBUTE -> INSERT_ATTRIBUTE') !== false) {
                $suggestions[] = [
                    'priority' => 'СРЕДНО',
                    'issue' => 'Модел SELECT->INSERT за атрибути се повтаря ' . $count . ' пъти',
                    'recommendation' => 'Предварително зареди всички атрибути в кеш',
                    'method_to_use' => '_preloadAllAttributes()'
                ];
            }
        }

        return $suggestions;
    }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за групирана обработка на атрибути за множество продукти
     * Заменя множество извиквания на _processStandardProductAttributes с една batch операция
     * Модифициран за работа с обработени атрибути от конекторите
     * @param array $products_data Масив [product_id => ['processed_attributes' => [...], ...]]
     * @param int $language_id ID на езика
     * @param int $mfsc_id ID на конектора
     * @return int Брой обработени продукти
     */
    public function processBatchStandardProductAttributes($products_data, $language_id, $mfsc_id) {
        if (empty($products_data)) {
            return 0;
        }

        $this->writeToCronLog("Започва batch обработка на атрибути за " . count($products_data) . " продукта", 'mfs_attributes.log');

        // Стъпка 1: Събираме всички обработени атрибути от конекторите
        $all_product_attributes = []; // [product_id => [attribute_name => value]]
        $products_with_valid_attributes = [];
        $total_attributes = 0;

        foreach ($products_data as $product_id => $product_data) {

            // Проверяваме за обработени атрибути от конектора
            if (isset($product_data['processed_attributes']) && !empty($product_data['processed_attributes'])) {
                $product_attributes = [];
                $valid_attributes_count = 0;

                foreach ($product_data['processed_attributes'] as $attribute) {
                    if (empty($attribute['name']) || empty($attribute['value']) || empty($attribute['attribute_id'])) {
                        continue;
                    }

                    $product_attributes[$attribute['name']] = [
                        'value' => $attribute['value'],
                        'attribute_id' => $attribute['attribute_id']
                    ];
                    $valid_attributes_count++;
                    $total_attributes++;
                }

                if ($valid_attributes_count > 0) {
                    $all_product_attributes[$product_id] = $product_attributes;
                    $products_with_valid_attributes[$product_id] = true;
                }
            }
            // Fallback към стария формат ако няма обработени атрибути
            elseif (isset($product_data['attributes_data_source']) && !empty($product_data['attributes_data_source'])) {
                // $this->writeToCronLog("MultiFeed Syncer: Fallback към стария формат за продукт ID $product_id", 'mfs_attributes.log');

                // Намиране или създаване на основна атрибутна група за fallback
                $attribute_group_id = $this->_getOrCreateAttributeGroup('Характеристики', $language_id);
                if (!$attribute_group_id) {
                    continue;
                }

                $valid_attributes = [];
                foreach ($product_data['attributes_data_source'] as $attribute) {
                    if (empty($attribute['name']) || empty($attribute['value'])) {
                        continue;
                    }

                    if (!$this->_isValidAttributeValue($attribute['value'])) {
                        continue;
                    }

                    $valid_attributes[] = $attribute;
                }

                if (!empty($valid_attributes)) {
                    $products_with_valid_attributes[$product_id] = $valid_attributes;
                }
            }
        }

        if (empty($all_product_attributes)) {
            // $this->writeToCronLog("Няма валидни атрибути за обработка", 'mfs_attributes.log');
            // $this->writeToCronLog("DEBUG STATS METHOD: processBatchStandardProductAttributes returning 0 - no valid attributes", 'mfs_attributes.log');
            return 0;
        }

        $this->writeToCronLog("Намерени $total_attributes атрибута за " . count($products_with_valid_attributes) . " продукта", 'mfs_attributes.log');

        // Стъпка 2: Записваме всички атрибути директно (атрибутите вече са обработени от конектора)
        $inserted_count = $this->_directBatchInsertProcessedAttributes($all_product_attributes, $language_id);

        $processed_products = count($products_with_valid_attributes);
        $this->writeToCronLog("Завършена batch обработка - $processed_products продукта, $inserted_count атрибута записани", 'mfs_attributes.log');
        $this->writeToCronLog("DEBUG STATS METHOD: processBatchStandardProductAttributes returning $inserted_count attributes", 'mfs_attributes.log');

        return $inserted_count; // Връщаме броя записани атрибути, не броя продукти
    }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за записване на обработени атрибути от конекторите
     * @param array $all_product_attributes Масив [product_id => [attribute_name => ['value' => ..., 'attribute_id' => ...]]]
     * @param int $language_id ID на езика
     * @return int Брой записани атрибути
     */
    private function _directBatchInsertProcessedAttributes($all_product_attributes, $language_id) {
        if (empty($all_product_attributes)) {
            return 0;
        }

        $this->writeToCronLog("MultiFeed Syncer PROCESSED ATTRIBUTES: Започва записване на " . count($all_product_attributes) . " продукта с обработени атрибути", 'mfs_attributes.log');

        // Стъпка 1: Проверяваме кои атрибути вече съществуват
        $existing_attributes = [];
        $product_ids = array_keys($all_product_attributes);

        if (!empty($product_ids)) {
            $chunks = array_chunk($product_ids, 100);
            foreach ($chunks as $chunk) {
                $query = $this->_executeQuery("
                    SELECT product_id, attribute_id
                    FROM `" . DB_PREFIX . "product_attribute`
                    WHERE product_id IN (" . implode(',', $chunk) . ")
                    AND language_id = '" . (int)$language_id . "'
                ", true, 'mfs_attributes_SQLs.log');

                foreach ($query->rows as $row) {
                    $existing_attributes[$row['product_id']][$row['attribute_id']] = true;
                }
            }
        }

        // Стъпка 2: Подготвяме данните за INSERT
        $insert_values = [];
        $new_count = 0;
        $skipped_count = 0;

        foreach ($all_product_attributes as $product_id => $attributes) {
            foreach ($attributes as $attribute_name => $attribute_data) {
                $attribute_id = $attribute_data['attribute_id'];
                $value = $attribute_data['value'];

                // Проверяваме дали атрибутът вече съществува
                if (isset($existing_attributes[$product_id][$attribute_id])) {
                    $skipped_count++;
                    continue;
                }

                $insert_values[] = "('" . (int)$product_id . "', '" . (int)$attribute_id . "', '" . (int)$language_id . "', '" . $this->db->escape($value) . "')";
                $new_count++;
            }
        }

        $this->writeToCronLog("MultiFeed Syncer PROCESSED ATTRIBUTES: Подготвени $new_count нови атрибута, пропуснати $skipped_count съществуващи", 'mfs_attributes.log');

        // Стъпка 3: Изпълняваме batch INSERT само за новите атрибути
        if (!empty($insert_values)) {
            $chunks = array_chunk($insert_values, 100); // Разделяме на порции от 100
            $total_inserted = 0;

            foreach ($chunks as $chunk) {
                $sql = "INSERT IGNORE INTO `" . DB_PREFIX . "product_attribute` (product_id, attribute_id, language_id, text) VALUES " . implode(', ', $chunk);
                $this->_executeQuery($sql, true, 'mfs_attributes_SQLs.log');
                $total_inserted += count($chunk);
            }

            $this->writeToCronLog("MultiFeed Syncer PROCESSED ATTRIBUTES: Записани $total_inserted нови атрибута с batch операция", 'mfs_attributes.log');
            return $total_inserted;
        }

        return 0;
    }

    // /**
    //  * Тестов метод за демонстрация на разликата между старите и новите оптимизации
    //  * @param array $test_products_data Тестови данни за продукти
    //  * @param int $language_id ID на езика
    //  * @param int $mfsc_id ID на конектора
    //  * @return array Резултати от сравнението
    //  */
    // public function testOptimizationComparison($test_products_data, $language_id, $mfsc_id) {
    //     if (empty($test_products_data)) {
    //         return ['error' => 'Няма тестови данни'];
    //     }

    //     $results = [
    //         'old_method' => [],
    //         'new_method' => [],
    //         'comparison' => []
    //     ];

    //     // Тест на старата логика (симулация)
    //     $start_time = microtime(true);
    //     $old_sql_count = 0;

    //     foreach ($test_products_data as $product_id => $product_data) {
    //         if (isset($product_data['attributes_data_source'])) {
    //             foreach ($product_data['attributes_data_source'] as $attribute) {
    //                 $old_sql_count++; // SELECT за всеки атрибут
    //                 $old_sql_count++; // INSERT за всеки атрибут (ако е нов)
    //             }
    //         }
    //     }

    //     $old_time = microtime(true) - $start_time;
    //     $results['old_method'] = [
    //         'time' => $old_time,
    //         'estimated_sql_queries' => $old_sql_count,
    //         'method' => 'Стара логика - по един атрибут наведнъж'
    //     ];

    //     // Тест на новата оптимизирана логика
    //     $start_time = microtime(true);

    //     // Симулираме новата логика
    //     $all_attribute_names = [];
    //     foreach ($test_products_data as $product_id => $product_data) {
    //         if (isset($product_data['attributes_data_source'])) {
    //             foreach ($product_data['attributes_data_source'] as $attribute) {
    //                 $all_attribute_names[] = $attribute['name'];
    //             }
    //         }
    //     }
    //     $unique_attributes = array_unique($all_attribute_names);

    //     $new_sql_count = 1; // Една SELECT заявка за всички атрибути
    //     $new_sql_count += count($unique_attributes); // INSERT заявки само за новите атрибути
    //     $new_sql_count += 1; // Една batch INSERT заявка за всички product_attribute записи

    //     $new_time = microtime(true) - $start_time;
    //     $results['new_method'] = [
    //         'time' => $new_time,
    //         'actual_sql_queries' => $new_sql_count,
    //         'method' => 'Нова оптимизирана логика - batch операции'
    //     ];

    //     // Сравнение
    //     $sql_improvement = $old_sql_count > 0 ? (($old_sql_count - $new_sql_count) / $old_sql_count) * 100 : 0;
    //     $time_improvement = $old_time > 0 ? (($old_time - $new_time) / $old_time) * 100 : 0;

    //     $results['comparison'] = [
    //         'sql_queries_reduction' => round($sql_improvement, 2) . '%',
    //         'time_improvement' => round($time_improvement, 2) . '%',
    //         'products_tested' => count($test_products_data),
    //         'unique_attributes' => count($unique_attributes),
    //         'total_attributes' => count($all_attribute_names)
    //     ];

    //     return $results;
    // }

    // /**
    //  * Debug метод за анализ на текущото състояние на оптимизациите
    //  * @return array Детайлен анализ
    //  */
    // public function debugOptimizationStatus() {
    //     $debug_info = [
    //         'optimization_methods_exist' => [],
    //         'integration_status' => [],
    //         'recommendations' => []
    //     ];

    //     // Проверка дали оптимизираните методи съществуват
    //     $debug_info['optimization_methods_exist'] = [
    //         '_batchGetOrCreateAttributes' => method_exists($this, '_batchGetOrCreateAttributes'),
    //         '_batchInsertProductAttributesOptimized' => method_exists($this, '_batchInsertProductAttributesOptimized'),
    //         '_directBatchInsertProductAttributes' => method_exists($this, '_directBatchInsertProductAttributes'),
    //         'processBatchStandardProductAttributes' => method_exists($this, 'processBatchStandardProductAttributes')
    //     ];

    //     // Анализ на интеграционното състояние
    //     $debug_info['integration_status'] = [
    //         'super_optimized_method_created' => method_exists($this, 'processBatchStandardProductAttributes'),
    //         'direct_batch_method_created' => method_exists($this, '_directBatchInsertProductAttributes'),
    //         'old_method_still_exists' => method_exists($this, '_processStandardProductAttributes'),
    //         'batch_get_create_exists' => method_exists($this, '_batchGetOrCreateAttributes')
    //     ];

    //     // Препоръки
    //     if ($debug_info['optimization_methods_exist']['_batchGetOrCreateAttributes'] &&
    //         $debug_info['optimization_methods_exist']['_directBatchInsertProductAttributes']) {
    //         $debug_info['recommendations'][] = "✅ Всички оптимизирани методи са създадени успешно";
    //         $debug_info['recommendations'][] = "✅ Новият _directBatchInsertProductAttributes() ще предотврати дублирането";
    //         $debug_info['recommendations'][] = "🎯 Следващо тестване ще покаже значително подобрение";
    //     } else {
    //         $debug_info['recommendations'][] = "❌ Някои оптимизирани методи липсват";
    //     }

    //     return $debug_info;
    // }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за създаване на атрибути с истински batch операции
     * Заменя множество единични INSERT заявки с 2 batch заявки
     * @param array $attribute_names Масив с имена на атрибути за създаване
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return array Масив [attribute_name => attribute_id]
     */
    private function _superBatchCreateAttributes($attribute_names, $attribute_group_id, $language_id) {
        if (empty($attribute_names)) {
            return [];
        }

        $result = [];
        $this->writeToCronLog("MultiFeed Syncer SUPER BATCH: Създаване на " . count($attribute_names) . " атрибута с истински batch операции");

        // Стъпка 1: Batch INSERT за oc_attribute
        $attribute_values = [];
        foreach ($attribute_names as $name) {
            $attribute_values[] = "('" . (int)$attribute_group_id . "', '0')";
        }

        $sql_attributes = "INSERT INTO `" . DB_PREFIX . "attribute` (`attribute_group_id`, `sort_order`) VALUES " . implode(', ', $attribute_values);
        $this->_executeQuery($sql_attributes, true, 'mfs_attributes.log');

        // Стъпка 2: Получаваме ID-тата на новосъздадените атрибути със сигурна заявка
        $last_id = $this->_getLastId();
        $first_id = $last_id - count($attribute_names) + 1;

        // Стъпка 3: Batch INSERT за oc_attribute_description
        $description_values = [];
        foreach ($attribute_names as $index => $name) {
            $attribute_id = $first_id + $index;
            $description_values[] = "('" . (int)$attribute_id . "', '" . (int)$language_id . "', '" . $this->db->escape($name) . "')";
            $result[$name] = $attribute_id;
        }

        $sql_descriptions = "INSERT INTO `" . DB_PREFIX . "attribute_description` (`attribute_id`, `language_id`, `name`) VALUES " . implode(', ', $description_values);
        $this->_executeQuery($sql_descriptions, true, 'mfs_attributes.log');

        $this->writeToCronLog("MultiFeed Syncer SUPER BATCH: Създадени " . count($attribute_names) . " атрибута с 2 batch INSERT заявки вместо " . (count($attribute_names) * 2) . " отделни заявки", 'mfs_attributes.log');

        return $result;
    }

    /**
     * УЛТРА ОПТИМИЗИРАН метод за групирано създаване/намиране на атрибути
     * ИЗПОЛЗВА САМО КЕША - БЕЗ SQL ЗАЯВКИ ЗА ТЪРСЕНЕ
     * @param array $attribute_names Масив с имена на атрибути
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return array Масив [attribute_name => attribute_id]
     */
    private function _ultraBatchGetOrCreateAttributes($attribute_names, $attribute_group_id, $language_id) {
        if (empty($attribute_names)) {
            return [];
        }

        $result = [];
        $names_to_create = [];

        $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE OPTIMIZED: Започва обработка на " . count($attribute_names) . " атрибута САМО с кеш", 'mfs_attributes.log');
        $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE DEBUG: Кеш съдържа " . count($this->preloaded_attributes) . " предварително заредени атрибута", 'mfs_attributes.log');

        // СТЪПКА 1: Проверяваме САМО в предварително заредения кеш с нормализирани ключове - БЕЗ SQL заявки
        foreach ($attribute_names as $name) {
            // ОПТИМИЗАЦИЯ: Използваме lowercase нормализирано име вместо MD5 хеш
            $normalized_name = mb_strtolower($name);
            $cache_key = $normalized_name . '_' . $attribute_group_id . '_' . $language_id;

            $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE OPTIMIZED DEBUG: Търсене на атрибут '{$name}' с нормализиран ключ '{$cache_key}'", 'mfs_attributes.log');

            if (isset($this->preloaded_attributes[$cache_key])) {
                // Намерен в кеша
                $result[$name] = $this->preloaded_attributes[$cache_key];
                $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE OPTIMIZED HIT: Атрибут '{$name}' намерен в кеша с ID " . $this->preloaded_attributes[$cache_key], 'mfs_attributes.log');
            } else {
                // Не е намерен в кеша - трябва да се създаде
                $names_to_create[] = $name;
                $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE OPTIMIZED MISS: Атрибут '{$name}' не е намерен в кеша - ще се създаде", 'mfs_attributes.log');
            }
        }

        $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE OPTIMIZED: Намерени " . count($result) . " атрибута в кеша, " . count($names_to_create) . " за създаване", 'mfs_attributes.log');

        // СТЪПКА 2: Създаваме липсващите атрибути с супер batch операции И обновяваме кешовете
        if (!empty($names_to_create)) {
            $new_attributes = $this->_superBatchCreateAttributes($names_to_create, $attribute_group_id, $language_id);
            $result = array_merge($result, $new_attributes);

            // КРИТИЧНО: Обновяваме всички кешове с новосъздадените атрибути
            $this->_updateAttributesCacheWithNewData($new_attributes, $attribute_group_id, $language_id);
        }

        $this->writeToCronLog("MultiFeed Syncer ULTRA CACHE OPTIMIZED: Завършена обработка - " . count($result) . " атрибута готови за използване", 'mfs_attributes.log');
        return $result;
    }

    // /**
    //  * Демонстрационен метод за сравняване на старите и новите batch операции
    //  * @param array $attribute_names Тестови имена на атрибути
    //  * @param int $attribute_group_id ID на атрибутната група
    //  * @param int $language_id ID на езика
    //  * @return array Сравнение на ефективността
    //  */
    // public function demonstrateBatchOptimization($attribute_names, $attribute_group_id, $language_id) {
    //     if (empty($attribute_names)) {
    //         return ['error' => 'Няма атрибути за тестване'];
    //     }

    //     $comparison = [
    //         'old_method' => [
    //             'description' => 'Стара логика - по един атрибут наведнъж',
    //             'sql_queries' => count($attribute_names) * 2, // SELECT + INSERT за всеки
    //             'operations' => [
    //                 'SELECT заявки' => count($attribute_names),
    //                 'INSERT за oc_attribute' => count($attribute_names),
    //                 'INSERT за oc_attribute_description' => count($attribute_names)
    //             ]
    //         ],
    //         'new_method' => [
    //             'description' => 'Нова УЛТРА оптимизирана логика - истински batch операции',
    //             'sql_queries' => 3, // 1 SELECT + 1 batch INSERT + 1 batch INSERT
    //             'operations' => [
    //                 'SELECT заявки' => 1,
    //                 'Batch INSERT за oc_attribute' => 1,
    //                 'Batch INSERT за oc_attribute_description' => 1
    //             ]
    //         ]
    //     ];

    //     $old_total = $comparison['old_method']['sql_queries'];
    //     $new_total = $comparison['new_method']['sql_queries'];
    //     $improvement = (($old_total - $new_total) / $old_total) * 100;

    //     $comparison['improvement'] = [
    //         'sql_reduction_percentage' => round($improvement, 2) . '%',
    //         'sql_queries_saved' => $old_total - $new_total,
    //         'attributes_tested' => count($attribute_names),
    //         'efficiency_multiplier' => round($old_total / $new_total, 2) . 'x по-ефективно'
    //     ];

    //     return $comparison;
    // }

    /**
     * ОПТИМИЗИРАН метод за групирано добавяне на изображения в опашката за изтегляне
     * Заменя множество единични INSERT заявки с една batch операция
     * @param array $images_data Масив [['product_id' => X, 'url' => 'URL', 'is_main' => bool, 'sort_order' => int], ...]
     * @param int $mfsc_id ID на конектора
     * @return int Брой добавени изображения
     */
    private function _batchQueueImagesForDownload($images_data, $mfsc_id) {
        if (empty($images_data)) {
            return 0;
        }

        $valid_images = [];
        $skipped_count = 0;

        // Валидация на всички изображения
        foreach ($images_data as $image_data) {
            // Валидация на product_id
            if (empty($image_data['product_id']) || $image_data['product_id'] <= 0) {
                $this->writeToCronLog("ГРЕШКА: Невалиден product_id (" . $image_data['product_id'] . ") за изображение " . $image_data['url'] . ". Пропускаме.", 'mfs_images.log');
                $skipped_count++;
                continue;
            }

            // Валидация на URL
            if (empty(trim($image_data['url']))) {
                $this->writeToCronLog("ГРЕШКА: Празен URL за изображение на продукт ID " . $image_data['product_id'] . ". Пропускаме.", 'mfs_images.log');
                $skipped_count++;
                continue;
            }

            // Валидация на URL формат
            if (!filter_var($image_data['url'], FILTER_VALIDATE_URL)) {
                $this->writeToCronLog("ГРЕШКА: Невалиден URL формат (" . $image_data['url'] . ") за продукт ID " . $image_data['product_id'] . ". Пропускаме.", 'mfs_images.log');
                $skipped_count++;
                continue;
            }

            $valid_images[] = $image_data;
        }

        if (empty($valid_images)) {
            $this->writeToCronLog("MultiFeed Syncer Images BATCH: Няма валидни изображения за добавяне в опашката.", 'mfs_images.log');
            return 0;
        }

        // Подготвяме VALUES за batch INSERT
        $insert_values = [];
        foreach ($valid_images as $image_data) {
            $insert_values[] = "('" . (int)$image_data['product_id'] . "', '" . (int)$mfsc_id . "', '" . $this->db->escape($image_data['url']) . "', '" . (int)$image_data['is_main'] . "', '" . (int)$image_data['sort_order'] . "', 'pending', NOW())";
        }

        // Изпълняваме batch INSERT
        if (!empty($insert_values)) {
            $chunks = array_chunk($insert_values, 100); // Разделяме на порции от 100
            $total_inserted = 0;

            foreach ($chunks as $chunk) {
                $sql = "INSERT IGNORE INTO `" . DB_PREFIX . "product_image_download_queue`
                        (`product_id`, `mfsc_id`, `image_url`, `is_main_image`, `sort_order`, `status`, `date_added`)
                        VALUES " . implode(', ', $chunk) . "
                        ON DUPLICATE KEY UPDATE `status` = 'pending', `attempts` = 0, `date_added` = NOW()";

                try {
                    $this->_executeQuery($sql, true, 'mfs_images.log');
                    $total_inserted += count($chunk);
                } catch (Exception $e) {
                    $this->writeToCronLog("ГРЕШКА при batch добавяне на изображения в опашката: " . $e->getMessage(), 'mfs_images.log');
                }
            }

            $this->writeToCronLog("MultiFeed Syncer Images BATCH: Добавени $total_inserted изображения в опашката с batch операция (пропуснати: $skipped_count)", 'mfs_images.log');
            return $total_inserted;
        }

        return 0;
    }

    /**
     * Събира всички изображения от продуктови данни за batch обработка
     * @param array $products_data Масив [product_id => product_data] или [sku => product_data]
     * @param array $product_id_map Опционален мапинг [sku => product_id] за нови продукти
     * @param array $existing_main_images Масив с продукти които вече имат главни изображения
     * @param array $existing_additional_images Масив с продукти които вече имат допълнителни изображения
     * @return array Масив с изображения готови за batch операция
     */
    private function _collectImagesForBatch($products_data, $product_id_map = [], $existing_main_images = [], $existing_additional_images = []) {
        $images_for_batch = [];

        foreach ($products_data as $key => $product_data) {
            // Определяме product_id
            $product_id = 0;
            if (is_numeric($key)) {
                // Ключът е product_id
                $product_id = (int)$key;
            } elseif (!empty($product_id_map[$key])) {
                // Ключът е SKU, използваме мапинга
                $product_id = (int)$product_id_map[$key];
            }

            if ($product_id <= 0) {
                continue; // Пропускаме невалидни product_id
            }

            // Събираме главното изображение
            $main_image_url = !empty($product_data['image']) ? $product_data['image'] : null;
            if ($main_image_url && filter_var($main_image_url, FILTER_VALIDATE_URL)) {
                // Проверяваме дали продуктът вече има главно изображение (за update операции)
                if (empty($existing_main_images[$product_id])) {
                    $images_for_batch[] = [
                        'product_id' => $product_id,
                        'url' => $main_image_url,
                        'is_main' => true,
                        'sort_order' => 0
                    ];
                }
            }

            // Събираме допълнителните изображения
            if (!empty($product_data['product_image']) && is_array($product_data['product_image'])) {
                // Проверяваме дали продуктът вече има допълнителни изображения (за update операции)
                if (empty($existing_additional_images[$product_id])) {
                    foreach ($product_data['product_image'] as $img_data) {
                        if (!empty($img_data['image']) &&
                            $img_data['image'] !== $main_image_url &&
                            filter_var($img_data['image'], FILTER_VALIDATE_URL)) {

                            $images_for_batch[] = [
                                'product_id' => $product_id,
                                'url' => $img_data['image'],
                                'is_main' => false,
                                'sort_order' => isset($img_data['sort_order']) ? (int)$img_data['sort_order'] : 0
                            ];
                        }
                    }
                }
            }
        }

        return $images_for_batch;
    }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за обработка на изображения за множество продукти
     * Заменя множество извиквания на _queueImageForDownload с една batch операция
     * @param array $products_data Масив с продуктови данни
     * @param array $product_id_map Опционален мапинг [sku => product_id] за нови продукти
     * @param array $existing_main_images Масив с продукти които вече имат главни изображения
     * @param array $existing_additional_images Масив с продукти които вече имат допълнителни изображения
     * @param int $mfsc_id ID на конектора
     * @return int Брой добавени изображения
     */
    public function processBatchProductImages($products_data, $product_id_map, $existing_main_images, $existing_additional_images, $mfsc_id) {
        if (empty($products_data)) {
            return 0;
        }

        $this->writeToCronLog("MultiFeed Syncer Images SUPER OPTIMIZED: Започва batch обработка на изображения за " . count($products_data) . " продукта", 'mfs_images.log');

        // Стъпка 1: Събираме всички изображения от всички продукти
        $all_images = $this->_collectImagesForBatch($products_data, $product_id_map, $existing_main_images, $existing_additional_images);

        if (empty($all_images)) {
            $this->writeToCronLog("MultiFeed Syncer Images SUPER OPTIMIZED: Няма изображения за обработка", 'mfs_images.log');
            return 0;
        }

        $this->writeToCronLog("MultiFeed Syncer Images SUPER OPTIMIZED: Намерени " . count($all_images) . " изображения за добавяне в опашката", 'mfs_images.log');

        // Стъпка 2: Добавяме всички изображения наведнъж с batch операция
        $inserted_count = $this->_batchQueueImagesForDownload($all_images, $mfsc_id);

        $this->writeToCronLog("MultiFeed Syncer Images SUPER OPTIMIZED: Завършена batch обработка - $inserted_count изображения добавени в опашката", 'mfs_images.log');

        return $inserted_count;
    }

    // /**
    //  * Демонстрационен метод за сравняване на старите и новите операции за изображения
    //  * @param array $products_data Тестови данни за продукти
    //  * @param int $mfsc_id ID на конектора
    //  * @return array Сравнение на ефективността
    //  */
    // public function demonstrateImageBatchOptimization($products_data, $mfsc_id) {
    //     if (empty($products_data)) {
    //         return ['error' => 'Няма продукти за тестване'];
    //     }

    //     // Симулираме събиране на изображения
    //     $all_images = $this->_collectImagesForBatch($products_data);
    //     $total_images = count($all_images);

    //     $comparison = [
    //         'old_method' => [
    //             'description' => 'Стара логика - по едно изображение наведнъж',
    //             'sql_queries' => $total_images, // Една INSERT заявка за всяко изображение
    //             'operations' => [
    //                 'INSERT заявки за изображения' => $total_images,
    //                 'Валидации' => $total_images,
    //                 'Обработка на грешки' => $total_images
    //             ]
    //         ],
    //         'new_method' => [
    //             'description' => 'Нова СУПЕР оптимизирана логика - batch операции',
    //             'sql_queries' => max(1, ceil($total_images / 100)), // Batch INSERT заявки (по 100 на заявка)
    //             'operations' => [
    //                 'Batch INSERT заявки' => max(1, ceil($total_images / 100)),
    //                 'Валидации (групирани)' => 1,
    //                 'Обработка на грешки (групирана)' => 1
    //             ]
    //         ]
    //     ];

    //     $old_total = $comparison['old_method']['sql_queries'];
    //     $new_total = $comparison['new_method']['sql_queries'];
    //     $improvement = $old_total > 0 ? (($old_total - $new_total) / $old_total) * 100 : 0;

    //     $comparison['improvement'] = [
    //         'sql_reduction_percentage' => round($improvement, 2) . '%',
    //         'sql_queries_saved' => $old_total - $new_total,
    //         'images_tested' => $total_images,
    //         'products_tested' => count($products_data),
    //         'efficiency_multiplier' => $old_total > 0 ? round($old_total / $new_total, 2) . 'x по-ефективно' : 'N/A'
    //     ];

    //     return $comparison;
    // }

    // /**
    //  * Комплексен тест на всички batch оптимизации - атрибути и изображения
    //  * @param array $test_products_data Тестови данни за продукти
    //  * @param int $mfsc_id ID на конектора
    //  * @return array Детайлно сравнение на ефективността
    //  */
    // public function testAllBatchOptimizations($test_products_data, $mfsc_id) {
    //     if (empty($test_products_data)) {
    //         return ['error' => 'Няма тестови данни'];
    //     }

    //     $language_id = (int)$this->config->get('config_language_id') ?: 1;

    //     // Симулираме събиране на атрибути и изображения
    //     $all_attribute_names = [];
    //     $all_images = [];
    //     $total_attributes = 0;

    //     foreach ($test_products_data as $product_id => $product_data) {
    //         // Събираме атрибути
    //         if (isset($product_data['attributes_data_source'])) {
    //             foreach ($product_data['attributes_data_source'] as $attribute) {
    //                 if (!empty($attribute['name'])) {
    //                     $all_attribute_names[] = $attribute['name'];
    //                     $total_attributes++;
    //                 }
    //             }
    //         }

    //         // Събираме изображения
    //         if (!empty($product_data['image'])) {
    //             $all_images[] = ['type' => 'main', 'url' => $product_data['image']];
    //         }
    //         if (!empty($product_data['product_image'])) {
    //             foreach ($product_data['product_image'] as $img) {
    //                 $all_images[] = ['type' => 'additional', 'url' => $img['image']];
    //             }
    //         }
    //     }

    //     $unique_attributes = array_unique($all_attribute_names);
    //     $total_images = count($all_images);

    //     $comparison = [
    //         'test_data' => [
    //             'products_count' => count($test_products_data),
    //             'total_attributes' => $total_attributes,
    //             'unique_attributes' => count($unique_attributes),
    //             'total_images' => $total_images
    //         ],
    //         'old_method' => [
    //             'description' => 'Стара логика - единични операции',
    //             'sql_queries' => [
    //                 'attributes_select' => count($unique_attributes),
    //                 'attributes_insert' => count($unique_attributes) * 2, // oc_attribute + oc_attribute_description
    //                 'product_attributes_insert' => $total_attributes,
    //                 'images_insert' => $total_images,
    //                 'total' => count($unique_attributes) + (count($unique_attributes) * 2) + $total_attributes + $total_images
    //             ]
    //         ],
    //         'new_method' => [
    //             'description' => 'Нова СУПЕР оптимизирана логика - batch операции',
    //             'sql_queries' => [
    //                 'attributes_select' => 1, // Една групирана SELECT заявка
    //                 'attributes_insert' => 2, // Една batch за oc_attribute + една за oc_attribute_description
    //                 'product_attributes_insert' => max(1, ceil($total_attributes / 100)), // Batch INSERT на порции
    //                 'images_insert' => max(1, ceil($total_images / 100)), // Batch INSERT на порции
    //                 'total' => 1 + 2 + max(1, ceil($total_attributes / 100)) + max(1, ceil($total_images / 100))
    //             ]
    //         ]
    //     ];

    //     $old_total = $comparison['old_method']['sql_queries']['total'];
    //     $new_total = $comparison['new_method']['sql_queries']['total'];
    //     $improvement = $old_total > 0 ? (($old_total - $new_total) / $old_total) * 100 : 0;

    //     $comparison['improvement'] = [
    //         'sql_reduction_percentage' => round($improvement, 2) . '%',
    //         'sql_queries_saved' => $old_total - $new_total,
    //         'efficiency_multiplier' => $old_total > 0 ? round($old_total / $new_total, 2) . 'x по-ефективно' : 'N/A',
    //         'performance_category' => $improvement > 90 ? 'ОТЛИЧНО' : ($improvement > 70 ? 'МНОГО ДОБРО' : ($improvement > 50 ? 'ДОБРО' : 'УМЕРЕНО'))
    //     ];

    //     return $comparison;
    // }

    /**
     * КРИТИЧЕН метод за обновяване на всички кешове с новосъздадени атрибути
     * Осигурява че новите атрибути са достъпни в следващите batch операции
     * @param array $new_attributes Масив [attribute_name => attribute_id] с новосъздадени атрибути
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     */
    private function _updateAttributesCacheWithNewData($new_attributes, $attribute_group_id, $language_id) {
        if (empty($new_attributes)) {
            return;
        }

        $updated_count = 0;
        foreach ($new_attributes as $attribute_name => $attribute_id) {
            // Обновяваме стария кеш
            $cache_key = md5($attribute_name . '_' . $attribute_group_id . '_' . $language_id);
            $this->attributes_cache[$cache_key] = $attribute_id;

            // Обновяваме предварително заредения кеш
            $this->preloaded_attributes[$cache_key] = $attribute_id;

            $updated_count++;
        }

        $this->writeToCronLog("MultiFeed Syncer CACHE UPDATE: Обновени $updated_count атрибута в кешовете за следващи batch операции", 'mfs_attributes.log');
    }

    /**
     * КРИТИЧЕН метод за обновяване на кеша за категории с новосъздадени категории
     * Осигурява че новите категории са достъпни в следващите операции
     * @param string $category_path Пълен път на новосъздадената категория
     * @param int $category_id ID на новосъздадената категория
     */
    private function _updateCategoriesCacheWithNewData($category_path, $category_id) {
        if (empty($category_path) || $category_id <= 0) {
            return;
        }

        // Обновяваме статичния кеш за категории
        self::$categories_cache[$category_path] = $category_id;

        $this->writeToCronLog("MultiFeed Syncer CACHE UPDATE: Добавена нова категория '$category_path' (ID: $category_id) в кеша", 'mfs_categories.log');
    }

    /**
     * СУПЕР ОПТИМИЗИРАН метод за batch обновяване на кеша за категории
     * Обновява кеша с множество нови категории наведнъж
     * @param array $new_categories Масив [category_path => category_id]
     */
    private function _batchUpdateCategoriesCache($new_categories) {
        if (empty($new_categories)) {
            return;
        }

        $updated_count = 0;
        foreach ($new_categories as $category_path => $category_id) {
            self::$categories_cache[$category_path] = $category_id;
            $updated_count++;
        }

        $this->writeToCronLog("MultiFeed Syncer CACHE BATCH UPDATE: Обновени $updated_count категории в кеша за следващи операции", 'mfs_categories.log');
    }

    // /**
    //  * Валидация на ефективността на кеширането при множество порции
    //  * Тества дали кешът работи правилно между различни batch операции
    //  * @param array $test_attribute_names Тестови имена на атрибути
    //  * @param int $attribute_group_id ID на атрибутната група
    //  * @param int $language_id ID на езика
    //  * @param int $chunks_count Брой порции за симулация
    //  * @return array Детайлен анализ на ефективността на кеширането
    //  */
    // public function validateCacheEfficiencyAcrossChunks($test_attribute_names, $attribute_group_id, $language_id, $chunks_count = 3) {
    //     if (empty($test_attribute_names) || $chunks_count < 2) {
    //         return ['error' => 'Невалидни параметри за тестване'];
    //     }

    //     // Разделяме атрибутите на порции
    //     $attribute_chunks = array_chunk($test_attribute_names, ceil(count($test_attribute_names) / $chunks_count));

    //     $cache_analysis = [
    //         'test_setup' => [
    //             'total_attributes' => count($test_attribute_names),
    //             'chunks_count' => count($attribute_chunks),
    //             'attributes_per_chunk' => array_map('count', $attribute_chunks)
    //         ],
    //         'without_cache_update' => [
    //             'description' => 'Без обновяване на кеша - всяка порция прави пълни SQL заявки',
    //             'sql_queries_per_chunk' => [],
    //             'total_sql_queries' => 0
    //         ],
    //         'with_cache_update' => [
    //             'description' => 'С обновяване на кеша - следващите порции използват кеша',
    //             'sql_queries_per_chunk' => [],
    //             'total_sql_queries' => 0
    //         ]
    //     ];

    //     // Симулация БЕЗ обновяване на кеша
    //     $created_attributes = [];
    //     foreach ($attribute_chunks as $chunk_index => $chunk_attributes) {
    //         $new_attributes_in_chunk = [];
    //         $existing_attributes_in_chunk = [];

    //         foreach ($chunk_attributes as $attr_name) {
    //             if (in_array($attr_name, $created_attributes)) {
    //                 $existing_attributes_in_chunk[] = $attr_name;
    //             } else {
    //                 $new_attributes_in_chunk[] = $attr_name;
    //                 $created_attributes[] = $attr_name;
    //             }
    //         }

    //         // БЕЗ кеш: всички атрибути изискват SQL заявки
    //         $sql_queries = count($chunk_attributes) + (count($new_attributes_in_chunk) * 2); // SELECT + CREATE
    //         $cache_analysis['without_cache_update']['sql_queries_per_chunk'][] = $sql_queries;
    //         $cache_analysis['without_cache_update']['total_sql_queries'] += $sql_queries;
    //     }

    //     // Симулация С обновяване на кеша
    //     $cached_attributes = [];
    //     foreach ($attribute_chunks as $chunk_index => $chunk_attributes) {
    //         $new_attributes_in_chunk = [];
    //         $cached_attributes_in_chunk = [];

    //         foreach ($chunk_attributes as $attr_name) {
    //             if (in_array($attr_name, $cached_attributes)) {
    //                 $cached_attributes_in_chunk[] = $attr_name; // Намерени в кеша
    //             } else {
    //                 $new_attributes_in_chunk[] = $attr_name;
    //                 $cached_attributes[] = $attr_name; // Добавяме в кеша
    //             }
    //         }

    //         // С кеш: само новите атрибути изискват SQL заявки
    //         if ($chunk_index == 0) {
    //             // Първата порция прави пълни заявки
    //             $sql_queries = count($chunk_attributes) + (count($new_attributes_in_chunk) * 2);
    //         } else {
    //             // Следващите порции използват кеша
    //             $sql_queries = count($cached_attributes_in_chunk) == 0 ? 1 : 0; // SELECT само ако има нови
    //             $sql_queries += count($new_attributes_in_chunk) * 2; // CREATE само за нови
    //         }

    //         $cache_analysis['with_cache_update']['sql_queries_per_chunk'][] = $sql_queries;
    //         $cache_analysis['with_cache_update']['total_sql_queries'] += $sql_queries;
    //     }

    //     // Изчисляваме подобрението
    //     $without_cache_total = $cache_analysis['without_cache_update']['total_sql_queries'];
    //     $with_cache_total = $cache_analysis['with_cache_update']['total_sql_queries'];
    //     $improvement = $without_cache_total > 0 ? (($without_cache_total - $with_cache_total) / $without_cache_total) * 100 : 0;

    //     $cache_analysis['cache_efficiency'] = [
    //         'sql_reduction_percentage' => round($improvement, 2) . '%',
    //         'sql_queries_saved' => $without_cache_total - $with_cache_total,
    //         'efficiency_multiplier' => $without_cache_total > 0 ? round($without_cache_total / $with_cache_total, 2) . 'x по-ефективно' : 'N/A',
    //         'cache_effectiveness' => $improvement > 50 ? 'ОТЛИЧНО' : ($improvement > 25 ? 'ДОБРО' : 'СЛАБО')
    //     ];

    //     return $cache_analysis;
    // }

    // /**
    //  * Демонстрационен тестов сценарий за ефективността на кеширането
    //  * Симулира реална синхронизация с множество порции и показва подобрението от кеширането
    //  * @param int $products_per_chunk Брой продукти на порция
    //  * @param int $chunks_count Брой порции
    //  * @param int $attributes_per_product Средно атрибути на продукт
    //  * @param float $attribute_reuse_percentage Процент повторно използване на атрибути между порции
    //  * @return array Детайлна демонстрация на ефективността
    //  */
    // public function demonstrateCacheEfficiencyScenario($products_per_chunk = 50, $chunks_count = 5, $attributes_per_product = 8, $attribute_reuse_percentage = 60.0) {
    //     // Генерираме симулирани данни
    //     $total_products = $products_per_chunk * $chunks_count;
    //     $total_attributes = $total_products * $attributes_per_product;
    //     $unique_attributes = round($total_attributes * (100 - $attribute_reuse_percentage) / 100);

    //     $scenario = [
    //         'simulation_parameters' => [
    //             'total_products' => $total_products,
    //             'products_per_chunk' => $products_per_chunk,
    //             'chunks_count' => $chunks_count,
    //             'attributes_per_product' => $attributes_per_product,
    //             'total_attributes' => $total_attributes,
    //             'unique_attributes' => $unique_attributes,
    //             'attribute_reuse_percentage' => $attribute_reuse_percentage . '%'
    //         ],
    //         'old_system_without_cache' => [
    //             'description' => 'Стара система БЕЗ кеширане между порции',
    //             'sql_queries_per_chunk' => [],
    //             'total_sql_queries' => 0
    //         ],
    //         'new_system_with_cache' => [
    //             'description' => 'Нова система С кеширане между порции',
    //             'sql_queries_per_chunk' => [],
    //             'total_sql_queries' => 0
    //         ]
    //     ];

    //     // Симулация на стара система БЕЗ кеширане
    //     for ($chunk = 1; $chunk <= $chunks_count; $chunk++) {
    //         $attributes_in_chunk = $products_per_chunk * $attributes_per_product;
    //         $unique_in_chunk = round($attributes_in_chunk * (100 - $attribute_reuse_percentage) / 100);

    //         // Всяка порция прави пълни SQL заявки за всички атрибути
    //         $sql_queries = $attributes_in_chunk + ($unique_in_chunk * 2); // SELECT + CREATE
    //         $scenario['old_system_without_cache']['sql_queries_per_chunk'][] = $sql_queries;
    //         $scenario['old_system_without_cache']['total_sql_queries'] += $sql_queries;
    //     }

    //     // Симулация на нова система С кеширане
    //     $cached_attributes_count = 0;
    //     for ($chunk = 1; $chunk <= $chunks_count; $chunk++) {
    //         $attributes_in_chunk = $products_per_chunk * $attributes_per_product;

    //         if ($chunk == 1) {
    //             // Първата порция прави пълни заявки
    //             $unique_in_chunk = round($attributes_in_chunk * (100 - $attribute_reuse_percentage) / 100);
    //             $sql_queries = $attributes_in_chunk + ($unique_in_chunk * 2);
    //             $cached_attributes_count = $unique_in_chunk;
    //         } else {
    //             // Следващите порции използват кеша
    //             $new_attributes = round($attributes_in_chunk * (100 - $attribute_reuse_percentage) / 100 / $chunks_count);
    //             $cached_attributes = $attributes_in_chunk - $new_attributes;

    //             $sql_queries = 1; // Една SELECT заявка за проверка
    //             $sql_queries += $new_attributes * 2; // CREATE само за нови
    //             $cached_attributes_count += $new_attributes;
    //         }

    //         $scenario['new_system_with_cache']['sql_queries_per_chunk'][] = $sql_queries;
    //         $scenario['new_system_with_cache']['total_sql_queries'] += $sql_queries;
    //     }

    //     // Изчисляваме общото подобрение
    //     $old_total = $scenario['old_system_without_cache']['total_sql_queries'];
    //     $new_total = $scenario['new_system_with_cache']['total_sql_queries'];
    //     $improvement = $old_total > 0 ? (($old_total - $new_total) / $old_total) * 100 : 0;

    //     $scenario['performance_improvement'] = [
    //         'sql_reduction_percentage' => round($improvement, 2) . '%',
    //         'sql_queries_saved' => $old_total - $new_total,
    //         'efficiency_multiplier' => $old_total > 0 ? round($old_total / $new_total, 2) . 'x по-ефективно' : 'N/A',
    //         'time_saved_estimate' => round(($old_total - $new_total) * 0.01, 2) . ' секунди', // ~10ms на SQL заявка
    //         'performance_category' => $improvement > 80 ? 'ОТЛИЧНО' : ($improvement > 60 ? 'МНОГО ДОБРО' : ($improvement > 40 ? 'ДОБРО' : 'УМЕРЕНО'))
    //     ];

    //     $scenario['real_world_impact'] = [
    //         'description' => 'Реално въздействие при синхронизация',
    //         'without_cache' => "При всяка синхронизация: {$old_total} SQL заявки",
    //         'with_cache' => "При всяка синхронизация: {$new_total} SQL заявки",
    //         'daily_savings' => "При 3 синхронизации дневно: " . (($old_total - $new_total) * 3) . " по-малко SQL заявки",
    //         'monthly_savings' => "Месечно спестяване: " . (($old_total - $new_total) * 3 * 30) . " SQL заявки"
    //     ];

    //     return $scenario;
    // }

    /**
     * Генерира SEO keyword от текст.
     * @param string $text
     * @return string
     */
    private function _generateSeoKeyword($text) {
        // Масив за транслитерация от кирилица към латиница
        $cyrillic = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ж' => 'zh', 'з' => 'z',
            'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p',
            'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch',
            'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya',
            'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ж' => 'Zh', 'З' => 'Z',
            'И' => 'I', 'Й' => 'Y', 'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N', 'О' => 'O', 'П' => 'P',
            'Р' => 'R', 'С' => 'S', 'Т' => 'T', 'У' => 'U', 'Ф' => 'F', 'Х' => 'H', 'Ц' => 'Ts', 'Ч' => 'Ch',
            'Ш' => 'Sh', 'Щ' => 'Sht', 'Ъ' => 'A', 'Ь' => 'Y', 'Ю' => 'Yu', 'Я' => 'Ya', ' ' => '-',
            'ьо' => 'yo', 'ЬО' => 'Yo', 'йо' => 'yo', 'ЙО' => 'Yo', 'дж' => 'dj', 'Дж' => 'Dj', 'ДЖ' => 'DJ'
        ];

        // Премахване на HTML тагове
        $text = strip_tags(html_entity_decode($text, ENT_QUOTES, 'UTF-8'));
        
        // Транслитерация на кирилица
        $text = str_replace(array_keys($cyrillic), array_values($cyrillic), $text);
        
        // Преобразуване в малки букви
        $text = mb_strtolower($text, 'UTF-8');
        
        // Замяна на не-буквено-цифрови символи с тирета
        $text = preg_replace('~[^\pL\d]+~u', '-', $text);
        
        // Премахване на начални и крайни тирета
        $text = trim($text, '-');
        
        // Премахване на повтарящи се тирета
        $text = preg_replace('~-+~', '-', $text);

        if (empty($text)) {
            return 'n-a-' . time(); // Резервен вариант, ако текстът е празен
        }

        return $text;
    }

    /**
     * Пакетно добавяне на нови продукти.
     * @param array $products_to_add Масив от продуктови данни [sku => product_data]
     * @param array $languages Масив с езиците от OpenCart
     * @param int $default_language_id ID на езика по подразбиране
     * @return array Мапинг на [sku => new_product_id]
     */
    private function _batchInsertProducts(array $products_to_add_map, array $languages, int $default_language_id, float $markup_percentage, string $identifier_field, array &$processed_info, array &$existing_seo_keywords, int $mfsc_id) {
        if (empty($products_to_add_map)) {
            return 0;
        }

        $successfully_added_main_product_count = 0;
        $product_chunks = array_chunk($products_to_add_map, self::MFS_BATCH_INSERT_CHUNK_SIZE, true); 
        
        $this->load->model('setting/setting'); 
        $default_stock_status_id = (int)$this->config->get('config_stock_status_id'); 
        $default_tax_class_id = 0; 
        $default_length_class_id = (int)$this->config->get('config_length_class_id');
        $default_weight_class_id = (int)$this->config->get('config_weight_class_id');
        $default_store_id = 0; 

        $chunk_number = 0;
        foreach ($product_chunks as $chunk_of_products_to_add) {
            $chunk_number++;
            $processed_info['log_details'][] = "Обработка на порция {$chunk_number}/" . count($product_chunks) . " за добавяне на продукти.";

            $product_values_chunk = [];
            $product_skus_in_chunk = []; 

            foreach ($chunk_of_products_to_add as $sku => $data) {
                $product_skus_in_chunk[] = $sku; 

                $image = '';
                $model = isset($data['model']) ? $this->db->escape($data['model']) : $this->db->escape($sku);
                $quantity = isset($data['quantity']) ? (int)$data['quantity'] : 0;
                $feed_price = isset($data['price']) ? (float)$data['price'] : 0.0;
               // $price = round($feed_price * (1 + $markup_percentage / 100), 4);
                $price = round($feed_price * 1.2, 4); // + 20% ДДС
                $stock_status_id = isset($data['stock_status_id']) ? (int)$data['stock_status_id'] : $default_stock_status_id;
                $manufacturer_id = isset($data['manufacturer_id']) ? (int)$data['manufacturer_id'] : 0;
                $shipping = isset($data['shipping']) ? (int)$data['shipping'] : 1;
                $tax_class_id = isset($data['tax_class_id']) ? (int)$data['tax_class_id'] : $default_tax_class_id;
                $date_available = isset($data['date_available']) ? $this->db->escape($data['date_available']) : date('Y-m-d');
                $weight = isset($data['weight']) ? (float)$data['weight'] : 0;
                $weight_class_id = isset($data['weight_class_id']) ? (int)$data['weight_class_id'] : $default_weight_class_id;
                $length = isset($data['length']) ? (float)$data['length'] : 0;
                $width = isset($data['width']) ? (float)$data['width'] : 0;
                $height = isset($data['height']) ? (float)$data['height'] : 0;
                $length_class_id = isset($data['length_class_id']) ? (int)$data['length_class_id'] : $default_length_class_id;
                $subtract = isset($data['subtract']) ? (int)$data['subtract'] : 1;
                $minimum = isset($data['minimum']) ? (int)$data['minimum'] : 1;
                $sort_order = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
                $status = 1;

                $product_values_chunk[] = "(
                    '" . $model . "', '" . $this->db->escape($sku) . "', 
                    '" . (isset($data['upc']) ? $this->db->escape($data['upc']) : '') . "', 
                    '" . (isset($data['ean']) ? $this->db->escape($data['ean']) : '') . "', 
                    '" . (isset($data['jan']) ? $this->db->escape($data['jan']) : '') . "', 
                    '" . (isset($data['isbn']) ? $this->db->escape($data['isbn']) : '') . "', 
                    '" . (isset($data['mpn']) ? $this->db->escape($data['mpn']) : '') . "', 
                    '" . (isset($data['location']) ? $this->db->escape($data['location']) : '') . "', 
                    " . $quantity . ", " . $stock_status_id . ", '" . $image . "', " . $manufacturer_id . ", 
                    " . $shipping . ", " . $price . ", " . (isset($data['points']) ? (int)$data['points'] : 0) . ", 
                    " . $tax_class_id . ", '" . $date_available . "', " . $weight . ", " . $weight_class_id . ", 
                    " . $length . ", " . $width . ", " . $height . ", " . $length_class_id . ", 
                    " . $subtract . ", " . $minimum . ", " . $sort_order . ", " . $status . ", 
                    NOW(), NOW()
                )";
            }

            $current_chunk_new_product_ids_map = []; 

            if (!empty($product_values_chunk)) {
                $sql_insert_product_chunk = "INSERT INTO `" . DB_PREFIX . "product` (
                    `model`, `sku`, `upc`, `ean`, `jan`, `isbn`, `mpn`, `location`, `quantity`, `stock_status_id`, `image`, `manufacturer_id`, 
                    `shipping`, `price`, `points`, `tax_class_id`, `date_available`, `weight`, `weight_class_id`, `length`, `width`, `height`, 
                    `length_class_id`, `subtract`, `minimum`, `sort_order`, `status`, `date_added`, `date_modified`
                ) VALUES " . implode(", ", $product_values_chunk);
                
                try {
                    $this->_executeQuery($sql_insert_product_chunk, true, 'mfs_products.log');
                    $num_in_chunk = count($product_skus_in_chunk);
                    
                    if ($this->test_mode) {
                        foreach ($product_skus_in_chunk as $original_sku_to_map) {
                            $dummy_product_id = $this->current_dummy_product_id_counter++;
                            $current_chunk_new_product_ids_map[$original_sku_to_map] = $dummy_product_id;
                            if (isset($this->log)) $this->log->write("MultiFeed Syncer (Test Mode): Присвоено dummy product_id {$dummy_product_id} за SKU: {$original_sku_to_map} в порция за добавяне.");
                        }
                        $successfully_added_main_product_count += $num_in_chunk;
                    } else {
                        $escaped_skus_for_query_chunk = [];
                        foreach($product_skus_in_chunk as $sku_in_chunk_orig) {
                            $escaped_skus_for_query_chunk[] = "'" . $this->db->escape($sku_in_chunk_orig) . "'";
                        }
                        
                        if (!empty($escaped_skus_for_query_chunk)) {
                            $query_new_ids_chunk = $this->_executeQuery("SELECT `product_id`, `sku` FROM `" . DB_PREFIX . "product` WHERE `sku` IN (" . implode(",", $escaped_skus_for_query_chunk) . ")", true, 'mfs_products.log');
                            if ($query_new_ids_chunk && $query_new_ids_chunk->rows) {
                                foreach ($query_new_ids_chunk->rows as $row) {
                                    $current_chunk_new_product_ids_map[$row['sku']] = (int)$row['product_id'];
                                    $successfully_added_main_product_count++;
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    $processed_info['errors'] += count($product_values_chunk);
                    $processed_info['log_details'][] = "Грешка при INSERT в oc_product за порция {$chunk_number}: " . $e->getMessage() . " SQL: Частичен изглед - " . substr($sql_insert_product_chunk, 0, 255);
                    continue; 
                }
            }
            unset($product_values_chunk);

            if (!empty($current_chunk_new_product_ids_map)) {
                // Предаваме $existing_seo_keywords по референция
                $this->_insertRelatedProductDataForChunk($current_chunk_new_product_ids_map, $chunk_of_products_to_add, $languages, $default_language_id, $default_store_id, $processed_info, $chunk_number, $existing_seo_keywords, $mfsc_id);
            }
            unset($current_chunk_new_product_ids_map, $chunk_of_products_to_add);

            if (self::MFS_SLEEP_BETWEEN_CHUNKS > 0) {
                sleep(self::MFS_SLEEP_BETWEEN_CHUNKS);
            }
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }
        unset($product_chunks);
        return $successfully_added_main_product_count;
    }

    private function _insertRelatedProductDataForChunk(array $new_product_ids_map_chunk, array $original_data_chunk, array $languages, int $default_language_id, int $default_store_id, array &$processed_info, int $chunk_number, array &$existing_seo_keywords, int $mfsc_id) {
        $product_description_values_ch = [];
        $product_to_store_values_ch = [];
        $url_alias_values_ch = [];
        $product_to_category_values_ch = [];

        $images_queued_count = 0;
        $categories_assigned_count = 0;

        foreach ($new_product_ids_map_chunk as $sku => $new_product_id) {
            if (!$new_product_id || !isset($original_data_chunk[$sku])) continue;

            $product_data_item = $original_data_chunk[$sku];

            // Проследяваме синхронизирания продукт
            $this->_trackSyncedProductInConnector($new_product_id, $mfsc_id);
            $product_to_store_values_ch[] = "(" . (int)$new_product_id . ", " . (int)$default_store_id . ")";

            $base_name_for_seo = '';
            foreach ($languages as $language) {
                $lang_id = (int)$language['language_id'];
                $name_val = "'N/A'"; // Обграждаме с кавички за SQL
                $description_val = "''"; // Празна стойност за SQL
                $meta_title_val = "'N/A'";
                
                $desc_data_source = null;
                if (isset($product_data_item['product_description'][$lang_id])) {
                    $desc_data_source = $product_data_item['product_description'][$lang_id];
                } elseif (isset($product_data_item['product_description'][$default_language_id])) { 
                    $desc_data_source = $product_data_item['product_description'][$default_language_id];
                } elseif (isset($product_data_item['name'])) { 
                     $desc_data_source = ['name' => $product_data_item['name'], 'description' => (isset($product_data_item['description']) ? $product_data_item['description'] : '')];
                }

                $current_name_unescaped = 'N/A';
                if ($desc_data_source) {
                    $current_name_unescaped = isset($desc_data_source['name']) ? trim($desc_data_source['name']) : 'N/A';
                    if (empty($current_name_unescaped)) $current_name_unescaped = 'N/A';
                    $name_val = "'" . $this->db->escape($current_name_unescaped) . "'";
                    
                    $description_val = "'" . $this->db->escape(isset($desc_data_source['description']) ? $desc_data_source['description'] : '') . "'";
                    
                    $current_meta_title_unescaped = isset($desc_data_source['meta_title']) && !empty(trim($desc_data_source['meta_title'])) ? trim($desc_data_source['meta_title']) : $current_name_unescaped;
                    if (empty($current_meta_title_unescaped)) $current_meta_title_unescaped = 'N/A';
                    $meta_title_val = "'" . $this->db->escape($current_meta_title_unescaped) . "'";
                }
                
                if ($lang_id == $default_language_id && $current_name_unescaped !== 'N/A') {
                    $base_name_for_seo = $current_name_unescaped;
                }

                $product_description_values_ch[] = "(
                    " . (int)$new_product_id . ", " . $lang_id . ", " . $name_val . ", 
                    " . $description_val . ", '' /* tag */, " . $meta_title_val . ", 
                    '' /* meta_description */, '' /* meta_keyword */
                )";
            }

            // ОПТИМИЗАЦИЯ: Събираме изображенията за batch обработка вместо единични INSERT заявки
            // (Изображенията ще се обработят в края на метода с batch операция)

            // Присвояване на категории за нови продукти
            $assigned_categories = $this->_assignCategoriesToProduct($new_product_id, $product_data_item, $mfsc_id, true);
            if (!empty($assigned_categories)) {
                foreach ($assigned_categories as $category_id) {
                    $product_to_category_values_ch[] = "(" . (int)$new_product_id . ", " . (int)$category_id . ")";
                }
                $categories_assigned_count++;
            }

            // Ако $base_name_for_seo все още е празен, опитваме с default_language_name от $product_data_item или SKU
            if (empty(trim($base_name_for_seo)) || $base_name_for_seo === 'N/A') {
                $base_name_for_seo = isset($product_data_item['default_language_name']) && trim($product_data_item['default_language_name']) !== 'N/A' && !empty(trim($product_data_item['default_language_name']))
                                     ? $product_data_item['default_language_name']
                                     : $sku;
            }

            if (!empty(trim($base_name_for_seo))) {
                $keyword = $this->_generateSeoKeyword( (string) $base_name_for_seo );
                $original_keyword = $keyword;
                $counter = 1;

                $lower_keyword = mb_strtolower($keyword, 'UTF-8');
                while (isset($existing_seo_keywords[$lower_keyword])) { // Проверка в кеша
                    $keyword = $original_keyword . '-' . $counter++;
                    $lower_keyword = mb_strtolower($keyword, 'UTF-8');
                }
                $url_alias_values_ch[] = "('product_id=" . (int)$new_product_id . "', '" . $this->db->escape($keyword) . "')";
                $existing_seo_keywords[$lower_keyword] = true; // Добавяме към кеша
            }
        }

        // Batch обработка на изображения за всички продукти наведнъж
        $images_queued_count = $this->processBatchProductImages(
            $original_data_chunk,
            $new_product_ids_map_chunk,
            [], // Няма съществуващи главни изображения за нови продукти
            [], // Няма съществуващи допълнителни изображения за нови продукти
            $mfsc_id
        );

        if ($images_queued_count > 0) {
            $processed_info['log_details'][] = "Добавени {$images_queued_count} изображения в опашката за изтегляне с batch операция (порция за добавяне {$chunk_number}).";
        }

        if ($categories_assigned_count > 0) {
            $processed_info['log_details'][] = "Присвоени категории на {$categories_assigned_count} продукта (порция за добавяне {$chunk_number}).";
        }

        try {
            if (!empty($product_description_values_ch)) {
                $sql_insert_desc_ch = "INSERT INTO `" . DB_PREFIX . "product_description` (`product_id`, `language_id`, `name`, `description`, `tag`, `meta_title`, `meta_description`, `meta_keyword`) VALUES " . implode(", ", $product_description_values_ch);
                $this->_executeQuery($sql_insert_desc_ch);
            }
            if (!empty($product_to_store_values_ch)) {
                $sql_insert_store_ch = "INSERT INTO `" . DB_PREFIX . "product_to_store` (`product_id`, `store_id`) VALUES " . implode(", ", $product_to_store_values_ch);
                $this->_executeQuery($sql_insert_store_ch);
            }
            if (!empty($url_alias_values_ch)) {
                $sql_insert_alias_ch = "INSERT INTO `" . DB_PREFIX . "seo_url` (`query`, `keyword`) VALUES " . implode(", ", $url_alias_values_ch);
                $this->_executeQuery($sql_insert_alias_ch);
            }
            if (!empty($product_to_category_values_ch)) {
                $sql_insert_category_ch = "INSERT INTO `" . DB_PREFIX . "product_to_category` (`product_id`, `category_id`) VALUES " . implode(", ", $product_to_category_values_ch);
                $this->_executeQuery($sql_insert_category_ch);
            }
        } catch (Exception $e) {
            $processed_info['errors'] += count($new_product_ids_map_chunk);
            $processed_info['log_details'][] = "Грешка при INSERT на свързани данни (description/store/alias) за порция {$chunk_number}: " . $e->getMessage();
        }

        // Обработка на атрибути за стандартната OpenCart атрибутна система
        $language_id = (int)$this->config->get('config_language_id') ?: 1;

        // Оптимизирано предварително зареждане на атрибути
        if (!self::$attributes_cache_loaded) {
            $this->_preloadAllAttributes($language_id);
            self::$attributes_cache_loaded = true;
            $this->writeToCronLog("MultiFeed Syncer: Атрибути заредени в статичния кеш.", 'mfs_attributes.log');
        } else {
            $this->writeToCronLog("MultiFeed Syncer: Използване на кеширани атрибути.", 'mfs_attributes.log');
        }


        // Групирана обработка на атрибути за всички продукти наведнъж
        $products_for_batch_attributes = [];
        foreach ($new_product_ids_map_chunk as $sku => $product_id) {
            if (isset($original_data_chunk[$sku]) && $product_id > 0) {
                $product_data = $original_data_chunk[$sku];
                if (isset($product_data['attributes_data_source']) && !empty($product_data['attributes_data_source'])) {
                    $products_for_batch_attributes[$product_id] = $product_data;
                }
            }
        }

        $standard_attributes_processed_count = 0;
        $this->writeToCronLog("DEBUG STATS: products_for_batch_attributes count: " . count($products_for_batch_attributes), 'mfs_attributes.log');
        if (!empty($products_for_batch_attributes)) {
            $standard_attributes_processed_count = $this->processBatchStandardProductAttributes(
                $products_for_batch_attributes,
                $language_id,
                $mfsc_id
            );
            $this->writeToCronLog("DEBUG STATS: processBatchStandardProductAttributes returned: " . $standard_attributes_processed_count, 'mfs_attributes.log');
        }

        if ($standard_attributes_processed_count > 0) {
            $processed_info['log_details'][] = "Добавени стандартни атрибути за {$standard_attributes_processed_count} нови продукта с batch операция (порция за добавяне {$chunk_number}).";
            $processed_info['standard_attributes'] += $standard_attributes_processed_count;
            $this->writeToCronLog("DEBUG STATS: Updated processed_info[standard_attributes] to: " . $processed_info['standard_attributes'], 'mfs_attributes.log');
        } else {
            $this->writeToCronLog("DEBUG STATS: No standard attributes processed (count was 0)", 'mfs_attributes.log');
        }

        // OCFilter интеграцията е премахната

        unset($product_description_values_ch, $product_to_store_values_ch, $url_alias_values_ch, $original_data_chunk);
    }


    /**
     * Пакетно актуализира продукти: цена и количество се обновяват веднага,
     * а изображенията се поставят в опашка за асинхронно изтегляне.
     */
    private function _batchUpdateProducts(array $products_to_update_map, float $markup_percentage, string $identifier_field, array &$processed_info, array &$existing_seo_keywords, int $mfsc_id) {
        if (empty($products_to_update_map)) {
            return 0;
        }

        // 1. Извличаме предварително съществуващите локални изображения за всички продукти, които ще се актуализират.
        $product_ids_for_check = array_keys($products_to_update_map);
        $existing_main_images = [];
        $existing_additional_images = [];

        if (!empty($product_ids_for_check)) {
            // Вземаме основните изображения
            $query_main = $this->_executeQuery("SELECT product_id, image FROM `" . DB_PREFIX . "product` WHERE product_id IN (" . implode(',', $product_ids_for_check) . ")", $do_log = false, 'mfs_products_SQLs.log');

            foreach ($query_main->rows as $row) {

                $image_valid = !empty($row['image']) && !filter_var($row['image'], FILTER_VALIDATE_URL);

                // $this->writeToCronLog("Validate main image: " . $row['image'] . " - " . json_encode($image_valid));

                // Записваме пътя, само ако не е празен и не е URL (т.е. е локален път)
                if ($image_valid) {
                    $existing_main_images[$row['product_id']] = $row['image'];
                }
            }

            unset($query_main);

            // Вземаме допълнителните изображения
            $query_additional = $this->_executeQuery("SELECT product_id FROM `" . DB_PREFIX . "product_image` WHERE product_id IN (" . implode(',', $product_ids_for_check) . ")", $do_log = false, 'mfs_products_SQLs.log');
            foreach ($query_additional->rows as $row) {
                // Тук е достатъчно само да маркираме, че продуктът има поне едно допълнително изображение
                $existing_additional_images[$row['product_id']] = true;
            }

            unset($query_additional);
        }

        // 2. ОПТИМИЗАЦИЯ: Използваме предварително заредения глобален кеш за продукти без категории
        $products_without_categories = self::$products_without_categories_cache;
        if (!empty($product_ids_for_check)) {
            $processed_info['log_details'][] = "Намерени " . count($products_without_categories) . " продукта без категории от общо " . count($product_ids_for_check) . " за обновяване (от глобален кеш).";
            $this->writeToCronLog("Намерени " . count($products_without_categories) . " продукта без категории от общо " . count($product_ids_for_check) . " за обновяване (от глобален кеш).", 'mfs_categories.log');
        }

        // $this->writeToCronLog("Намерени " . count($products_to_update_map) . " продукта за обновяване.");

        // Batch обработка на изображения за всички продукти наведнъж
        $images_queued_count = $this->processBatchProductImages(
            $products_to_update_map,
            [], // Няма нужда от мапинг за съществуващи продукти
            $existing_main_images,
            $existing_additional_images,
            $mfsc_id
        );

        $product_chunks = array_chunk($products_to_update_map, self::MFS_BATCH_UPDATE_CHUNK_SIZE, true);
        $total_successfully_updated_count = 0;
        $categories_assigned_count = 0;

        $chunk_number = 0;
        foreach ($product_chunks as $chunk) {
            $chunk_number++;
            if (empty($chunk)) continue;

            $price_cases = [];
            $quantity_cases = [];
            $product_to_category_values_chunk = [];

            foreach ($chunk as $product_id => $data) {
                // Проследяваме синхронизирания продукт
                $this->_trackSyncedProductInConnector($product_id, $mfsc_id);

                // 1. Подготовка на данни за незабавна актуализация (цена и количество)
                if (array_key_exists('price', $data)) {
                   // $calculated_price = round((float)$data['price'] * (1 + $markup_percentage / 100), 4);
                    $calculated_price = round((float)$data['price'] * 1.2, 4); // + 20% ДДС
                    $price_cases[] = "WHEN " . (int)$product_id . " THEN " . (float)$calculated_price;
                }
                if (array_key_exists('quantity', $data)) {
                    $quantity_cases[] = "WHEN " . (int)$product_id . " THEN " . (int)$data['quantity'];
                }

                // Присвояване на категории за продукти без зададена категория (оптимизирано)
                if (isset($products_without_categories[$product_id])) {
                    $assigned_categories = $this->_assignCategoriesToProductOptimized($product_id, $data, $mfsc_id);
                    if (!empty($assigned_categories)) {
                        foreach ($assigned_categories as $category_id) {
                            $product_to_category_values_chunk[] = "(" . (int)$product_id . ", " . (int)$category_id . ")";
                        }
                        $categories_assigned_count++;
                    }
                }
            }
            
            // Изпълняваме заявката САМО за цена и количество
            $affected_rows = $this->_executeUpdateProductQuery($chunk, $price_cases, $quantity_cases, [], $do_log = true, 'mfs_products_update_SQLs.log'); // Подаваме празен масив за изображения
            if ($affected_rows > 0) {
                 $processed_info['log_details'][] = "Засегнати {$affected_rows} продукта в основната таблица (порция {$chunk_number}).";
            }
            $total_successfully_updated_count += $affected_rows;

            $this->writeToCronLog("Product to category values chunk: " . print_r($product_to_category_values_chunk, true), 'mfs_products.log');

            // Вмъкваме категориите за тази порция
            if (!empty($product_to_category_values_chunk)) {
                try {
                    $sql_insert_category_chunk = "INSERT INTO `" . DB_PREFIX . "product_to_category` (`product_id`, `category_id`) VALUES " . implode(", ", $product_to_category_values_chunk);
                    $this->_executeQuery($sql_insert_category_chunk, $do_log = true, 'mfs_products_insert_SQLs.log');
                } catch (Exception $e) {
                    $processed_info['log_details'][] = "Грешка при присвояване на категории (порция {$chunk_number}): " . $e->getMessage();
                }
            }
        }

        if ($images_queued_count > 0) {
            $processed_info['log_details'][] = "Общо {$images_queued_count} изображения са добавени в опашката за изтегляне.";
        }

        if ($categories_assigned_count > 0) {
            $processed_info['log_details'][] = "Присвоени категории на {$categories_assigned_count} продукта при обновяване.";
        }

        // 3. Генериране на липсващи SEO URL адреси (тази операция остава, тъй като е бърза)
        $this->_generateMissingSeoUrlsForUpdate($products_to_update_map, $identifier_field, $processed_info, $existing_seo_keywords);


        // 5. Обработка на атрибути за стандартната OpenCart атрибутна система при обновяване
        $language_id = (int)$this->config->get('config_language_id') ?: 1;

        // Оптимизирано предварително зареждане на атрибути (ако не са вече заредени)
        if (!self::$attributes_cache_loaded) {
            $this->_preloadAllAttributes($language_id);
            self::$attributes_cache_loaded = true;
            $this->writeToCronLog("MultiFeed Syncer: Атрибути заредени в статичния кеш (при обновяване).", 'mfs_attributes.log');
        }

        // Предварително намираме всички продукти без атрибути с една заявка
        $product_ids_to_check = array_keys($products_to_update_map);
        $products_without_attributes = [];

        if (!empty($product_ids_to_check)) {
            $query_no_attributes = $this->_executeQuery("
                SELECT DISTINCT p.product_id
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_attribute` pa ON (p.product_id = pa.product_id)
                WHERE p.product_id IN (" . implode(',', $product_ids_to_check) . ")
                AND pa.product_id IS NULL
            ", $do_log = false);

            foreach ($query_no_attributes->rows as $row) {
                $products_without_attributes[(int)$row['product_id']] = true;
            }
        }

        // Групирана обработка на атрибути за продукти без атрибути
        $products_for_batch_attributes = [];
        foreach ($products_to_update_map as $product_id => $product_data) {
            if (isset($product_data['attributes_data_source']) && !empty($product_data['attributes_data_source'])) {
                if (isset($products_without_attributes[$product_id])) {
                    $products_for_batch_attributes[$product_id] = $product_data;
                }
            }
        }

        $standard_attributes_processed_count = 0;
        $this->writeToCronLog("DEBUG STATS UPDATE: products_for_batch_attributes count: " . count($products_for_batch_attributes), 'mfs_attributes.log');
        if (!empty($products_for_batch_attributes)) {
            $standard_attributes_processed_count = $this->processBatchStandardProductAttributes(
                $products_for_batch_attributes,
                $language_id,
                $mfsc_id
            );
            // $this->writeToCronLog("DEBUG STATS UPDATE: processBatchStandardProductAttributes returned: " . $standard_attributes_processed_count, 'mfs_attributes.log');
        }

        if ($standard_attributes_processed_count > 0) {
            $processed_info['log_details'][] = "Добавени стандартни атрибути за {$standard_attributes_processed_count} продукта без съществуващи стандартни атрибути с batch операция.";
            $processed_info['standard_attributes'] += $standard_attributes_processed_count;
            // $this->writeToCronLog("DEBUG STATS UPDATE: Updated processed_info[standard_attributes] to: " . $processed_info['standard_attributes'], 'mfs_attributes.log');
        } else {
            // $this->writeToCronLog("DEBUG STATS UPDATE: No standard attributes processed (count was 0)", 'mfs_attributes.log');
        }

        return $total_successfully_updated_count;
    }

    /**
     * Помощен метод за добавяне на URL на изображение в опашката за изтегляне.
     *
     * @param int $product_id
     * @param string $url
     * @param bool $is_main
     * @param int $sort_order
     * @param int $mfsc_id
     */
    private function _queueImageForDownload(int $product_id, string $url, bool $is_main, int $sort_order, int $mfsc_id) {
        // Валидация на product_id - не позволяваме 0 или отрицателни стойности
        if ($product_id <= 0) {
            $this->writeToCronLog("ГРЕШКА: Невалиден product_id ($product_id) за изображение $url. Пропускаме добавянето в опашката.", 'mfs_images.log');
            return;
        }

        // Валидация на URL
        if (empty(trim($url))) {
            $this->writeToCronLog("ГРЕШКА: Празен URL за изображение на продукт ID $product_id. Пропускаме добавянето в опашката.", 'mfs_images.log');
            return;
        }

        $sql = "INSERT INTO `" . DB_PREFIX . "product_image_download_queue` SET
                    `product_id` = " . (int)$product_id . ",
                    `mfsc_id` = " . (int)$mfsc_id . ",
                    `image_url` = '" . $this->db->escape($url) . "',
                    `is_main_image` = " . (int)$is_main . ",
                    `sort_order` = " . (int)$sort_order . ",
                    `status` = 'pending',
                    `date_added` = NOW()
                ON DUPLICATE KEY UPDATE `status` = 'pending', `attempts` = 0, `date_added` = NOW()";
        // ON DUPLICATE KEY UPDATE е полезно, ако има UNIQUE индекс върху (product_id, image_url),
        // за да се избегнат дубликати и просто да се "рестартира" задачата.
        // За целта трябва да добавите UNIQUE INDEX `product_image_url` (`product_id`, `image_url`(255)) към таблицата.

        try {
            $this->_executeQuery($sql);
        } catch (Exception $e) {
            $this->writeToCronLog("ГРЕШКА при добавяне на изображение в опашката: " . $e->getMessage() . " SQL: " . $sql, 'mfs_images.log');
        }
    }

    private function _executeUpdateProductQuery(array $chunk, array $price_cases, array $quantity_cases, array $main_image_cases, bool $do_log = true, $log_file = 'mfs_products.log') {
        $product_ids_in_chunk = array_keys($chunk);
        if (empty($product_ids_in_chunk)) {
            return 0;
        }

        $update_parts = [];
        if (!empty($price_cases)) {
            $update_parts[] = "`price` = (CASE `product_id` " . implode(" ", $price_cases) . " ELSE `price` END)";
        }
        if (!empty($quantity_cases)) {
            $update_parts[] = "`quantity` = (CASE `product_id` " . implode(" ", $quantity_cases) . " ELSE `quantity` END)";
        }
        if (!empty($main_image_cases)) {
            $update_parts[] = "`image` = (CASE `product_id` " . implode(" ", $main_image_cases) . " ELSE `image` END)";
        }

        if (empty($update_parts)) {
            return 0;
        }

        $sql = "UPDATE `" . DB_PREFIX . "product` SET " . implode(", ", $update_parts) . ", `date_modified` = NOW() WHERE `product_id` IN (" . implode(",", $product_ids_in_chunk) . ")";

        try {
            $this->_executeQuery($sql, $do_log, $log_file);
            return $this->test_mode ? count($product_ids_in_chunk) : $this->db->countAffected();
        } catch (Exception $e) {
            $this->writeToCronLog("Грешка при UPDATE на продукти: " . $e->getMessage(), 'mfs_products.log');
            return 0;
        }
    }

    private function _generateMissingSeoUrlsForUpdate(array $products_to_update_map, string $identifier_field, array &$processed_info, array &$existing_seo_keywords) {
        $product_ids_to_check = array_keys($products_to_update_map);
        if (empty($product_ids_to_check)) {
            return;
        }

        // Намираме кои от тези продукти вече имат SEO URL
        $product_ids_with_seo_url = [];
        $seo_check_chunks = array_chunk($product_ids_to_check, 200);
        foreach ($seo_check_chunks as $id_chunk) {
            $query_parts = array_map(function($pid) { return "'product_id=" . (int)$pid . "'"; }, $id_chunk);
            $seo_query = $this->_executeQuery("SELECT `query` FROM `" . DB_PREFIX . "seo_url` WHERE `query` IN (" . implode(",", $query_parts) . ")", false);
            if ($seo_query && $seo_query->rows) {
                foreach ($seo_query->rows as $row) {
                    if (preg_match('/product_id=(\d+)/', $row['query'], $matches)) {
                        $product_ids_with_seo_url[(int)$matches[1]] = true;
                    }
                }
            }
        }

        // Намираме продуктите БЕЗ SEO URL и генерираме за тях
        $new_url_alias_values = [];
        foreach ($products_to_update_map as $product_id => $data) {
            if (!isset($product_ids_with_seo_url[$product_id])) {
                $sku_for_log = isset($data[$identifier_field]) ? $data[$identifier_field] : (isset($data['sku_for_log']) ? $data['sku_for_log'] : 'ID:'.$product_id);
                $base_name = isset($data['default_language_name']) && $data['default_language_name'] !== 'N/A' ? $data['default_language_name'] : $sku_for_log;

                if (!empty(trim($base_name))) {
                    $keyword = $this->_generateSeoKeyword((string)$base_name);
                    $original_keyword = $keyword;
                    $counter = 1;
                    $lower_keyword = mb_strtolower($keyword, 'UTF-8');

                    while (isset($existing_seo_keywords[$lower_keyword])) {
                        $keyword = $original_keyword . '-' . $counter++;
                        $lower_keyword = mb_strtolower($keyword, 'UTF-8');
                    }
                    $new_url_alias_values[] = "('product_id=" . (int)$product_id . "', '" . $this->db->escape($keyword) . "')";
                    $existing_seo_keywords[$lower_keyword] = true; // Добавяме към кеша, за да избегнем дублиране в рамките на същата синхронизация
                    $processed_info['log_details'][] = "Генериран нов SEO URL '{$keyword}' за съществуващ продукт ID {$product_id}.";
                }
            }
        }
        
        // Вмъкваме новите SEO URL-и на порции
        if (!empty($new_url_alias_values)) {
            $seo_insert_chunks = array_chunk($new_url_alias_values, 100);
            foreach ($seo_insert_chunks as $seo_chunk) {
                $sql_insert_alias = "INSERT INTO `" . DB_PREFIX . "seo_url` (`query`, `keyword`) VALUES " . implode(", ", $seo_chunk);
                $this->_executeQuery($sql_insert_alias, false, 'mfs_seo_urls.log');
            }
        }
    }

    /**
     * Предварително зарежда всички продукти без категории в глобален кеш
     * @param array $product_ids Масив с product_id за проверка
     * @return void
     */
    private function _preloadProductsWithoutCategories($product_ids) {
        // if (empty($product_ids) || self::$products_without_categories_loaded) {
        //     return; // Няма продукти за проверка или вече е заредено
        // }

        if (self::$products_without_categories_loaded) {
            return; // Няма продукти за проверка или вече е заредено
        }

        // $query = $this->_executeQuery("
        //     SELECT DISTINCT p.product_id
        //     FROM `" . DB_PREFIX . "product` p
        //     LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
        //     WHERE p.product_id IN (" . implode(',', array_map('intval', $product_ids)) . ")
        //     AND ptc.product_id IS NULL
        // ", true, 'mfs_categories.log');

        $query = $this->_executeQuery("
            SELECT DISTINCT p.product_id
            FROM `" . DB_PREFIX . "product` p
            LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
            WHERE ptc.product_id IS NULL
        ", true, 'mfs_categories.log');



        self::$products_without_categories_cache = [];
        if ($query && $query->rows) {
            foreach ($query->rows as $row) {
                self::$products_without_categories_cache[(int)$row['product_id']] = true;
            }
        }

        self::$products_without_categories_loaded = true;
        $this->writeToCronLog("MultiFeed Syncer: Заредени " . count(self::$products_without_categories_cache) . " продукта без категории в глобалния кеш. IDS: " . implode(', ', array_keys(self::$products_without_categories_cache)), 'mfs_categories.log');
    }

    /**
     * Предварително зарежда всички съответствия на категории за конкретен конектор
     * @param int $mfsc_id ID на конектора
     * @return void
     */
    private function _preloadCategoriesMapping($mfsc_id) {
        if (isset(self::$categories_mapping_cache[$mfsc_id])) {
            return; // Вече е заредено за този конектор
        }

        $query = $this->_executeQuery("
            SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
            WHERE `mfsc_id` = '" . (int)$mfsc_id . "'
            ORDER BY `source_category_path` ASC
        ", false, 'mfs_categories.log');

        $mappings_map = [];
        if ($query && $query->rows) {
            foreach ($query->rows as $row) {
                $source_path = $this->decodeCategoryPath($row['source_category_path']);
                $target_path = $this->decodeCategoryPath($row['target_category_path']);
                $mappings_map[$source_path] = $target_path;
            }
        }

        self::$categories_mapping_cache[$mfsc_id] = $mappings_map;
        // $this->writeToCronLog("MultiFeed Syncer: Заредени " . count($mappings_map) . " съответствия на категории за конектор {$mfsc_id}.", 'mfs_categories.log');
    }

    /**
     * Получава съответствията на категориите за конкретен конектор
     * @param int $mfsc_id ID на конектора
     * @return array
     */
    public function getCategoriesMapping($mfsc_id) {
        $query = $this->_executeQuery("
            SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
            WHERE `mfsc_id` = '" . (int)$mfsc_id . "'
            ORDER BY `source_category_path` ASC
        ", false, 'mfs_categories.log');

        // Декодираме HTML entities в резултатите
        $decoded_rows = [];
        foreach ($query->rows as $row) {
            $row['source_category_path'] = $this->decodeCategoryPath($row['source_category_path']);
            $row['target_category_path'] = $this->decodeCategoryPath($row['target_category_path']);
            $decoded_rows[] = $row;
        }

        return $decoded_rows;
    }

    /**
     * Присвоява категории на продукт според съответствията от таблицата
     * @param int $product_id ID на продукта
     * @param array $product_data Данни на продукта
     * @param int $mfsc_id ID на конектора
     * @param bool $is_new_product Дали е нов продукт (true) или съществуващ (false)
     * @return array Масив с ID-та на категориите за присвояване
     */
    private function _assignCategoriesToProduct($product_id, $product_data, $mfsc_id, $is_new_product) {
        $assigned_category_ids = [];

        // За съществуващи продукти проверяваме дали вече имат категория
        if (!$is_new_product && !$this->_shouldAssignCategories($product_id)) {
            return $assigned_category_ids;
        }

        // Извличаме CategoryBranch данните от продукта
        $category_branches = $this->_extractCategoryBranches($product_data);
        if (empty($category_branches)) {
            return $assigned_category_ids;
        }

        // ОПТИМИЗАЦИЯ: Използваме предварително заредения глобален кеш
        $mappings_map = isset(self::$categories_mapping_cache[$mfsc_id]) ? self::$categories_mapping_cache[$mfsc_id] : [];

        // Обработваме всяка категория от продукта
        foreach ($category_branches as $category_branch) {
            // Преобразуваме формата от "Категория1|Категория2" в "Категория1 > Категория2"
            $source_path = str_replace('|', ' > ', $category_branch);

            // Търсим съответствие в таблицата
            if (isset($mappings_map[$source_path])) {
                $target_path = $mappings_map[$source_path];
                $category_id = $this->_getCategoryIdFromPath($target_path);
                if ($category_id > 0) {
                    // НОВА ЛОГИКА: Включваме и всички родителски категории
                    $parent_category_ids = $this->_getParentCategoryIds($category_id);
                    foreach ($parent_category_ids as $parent_id) {
                        $assigned_category_ids[] = $parent_id;
                    }
                }
            }
        }

        return array_unique($assigned_category_ids);
    }

    /**
     * Оптимизирана версия за присвояване на категории без проверка за съществуващи категории
     * Използва се когато вече знаем че продуктът няма категории
     * @param int $product_id ID на продукта
     * @param array $product_data Данни на продукта
     * @param int $mfsc_id ID на конектора
     * @return array Масив с ID-та на категориите за присвояване
     */
    private function _assignCategoriesToProductOptimized($product_id, $product_data, $mfsc_id) {
        $assigned_category_ids = [];

        // Извличаме CategoryBranch данните от продукта
        $category_branches = $this->_extractCategoryBranches($product_data);
        if (empty($category_branches)) {
            return $assigned_category_ids;
        }

        // ОПТИМИЗАЦИЯ: Използваме предварително заредения глобален кеш
        $mappings_map = isset(self::$categories_mapping_cache[$mfsc_id]) ? self::$categories_mapping_cache[$mfsc_id] : [];

        // Обработваме всяка категория от продукта
        foreach ($category_branches as $category_branch) {
            // Преобразуваме формата от "Категория1|Категория2" в "Категория1 > Категория2"
            $source_path = str_replace('|', ' > ', $category_branch);

            // Търсим съответствие в таблицата
            if (isset($mappings_map[$source_path])) {
                $target_path = $mappings_map[$source_path];
                $category_id = $this->_getCategoryIdFromPath($target_path);
                if ($category_id > 0) {
                    // НОВА ЛОГИКА: Включваме и всички родителски категории
                    $parent_category_ids = $this->_getParentCategoryIds($category_id);
                    foreach ($parent_category_ids as $parent_id) {
                        $assigned_category_ids[] = $parent_id;
                    }
                }
            }
        }

        return array_unique($assigned_category_ids);
    }

    /**
     * Проверява дали продуктът трябва да получи категории
     * ОПТИМИЗИРАНО: Използва предварително заредения глобален кеш вместо SQL заявка
     * @param int $product_id ID на продукта
     * @return bool true ако продуктът няма категории, false ако вече има
     */
    private function _shouldAssignCategories($product_id) {
        // ОПТИМИЗАЦИЯ: Използваме глобалния кеш ако е зареден
        if (self::$products_without_categories_loaded) {
            return isset(self::$products_without_categories_cache[(int)$product_id]);
        }

        // Fallback към SQL заявка ако кешът не е зареден (за съвместимост)
        $query = $this->_executeQuery("
            SELECT COUNT(*) as category_count
            FROM `" . DB_PREFIX . "product_to_category`
            WHERE `product_id` = '" . (int)$product_id . "'
        ", false, 'mfs_categories.log');

        return ($query && $query->row && (int)$query->row['category_count'] === 0);
    }

    /**
     * Извлича CategoryBranch данните от продуктовите данни
     * @param array $product_data Данни на продукта
     * @return array Масив с category branch пътища
     */
    private function _extractCategoryBranches($product_data) {
        $category_branches = [];

        // $this->writeToCronLog("Product data: " . print_r($product_data, true));

        // Проверяваме дали има categories_data_source (от конектора)
        if (!empty($product_data['categories_data_source']) && is_array($product_data['categories_data_source'])) {
            foreach ($product_data['categories_data_source'] as $category_data) {
                if (!empty($category_data['branch'])) {
                    $category_branches[] = $category_data['branch'];
                }
            }
        }

        return $category_branches;
    }

    /**
     * Зарежда всички OpenCart категории в кеша за по-ефективно търсене
     * Този метод се извиква веднъж в началото на синхронизацията
     * @return void
     */
    private function _loadCategoriesCache() {
        if (!empty(self::$categories_cache)) {
            return; // Кешът вече е зареден
        }

        // $this->writeToCronLog("MultiFeed Syncer: Зареждане на кеш за категории...");

        // Получаваме всички активни категории с техните имена
        $query = $this->_executeQuery("
            SELECT c.`category_id`, cd.`name`, c.`parent_id`
            FROM `" . DB_PREFIX . "category` c
            LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (c.`category_id` = cd.`category_id`)
            WHERE cd.`language_id` = '" . (int)$this->config->get('config_language_id') . "'
            AND c.`status` = '1'
            ORDER BY c.`category_id` ASC
        ", false);

        if (!$query || !$query->rows) {
            $this->writeToCronLog("MultiFeed Syncer: Няма намерени активни категории за кеширане.", true, 'mfs_categories.log');
            return;
        }

        // Създаваме временен масив за построяване на пътищата
        $categories_data = [];
        foreach ($query->rows as $row) {
            $category_id = (int)$row['category_id'];
            $parent_id = (int)$row['parent_id'];

            $categories_data[$category_id] = [
                'name' => $row['name'],
                'parent_id' => $parent_id
            ];

            // НОВА ОПТИМИЗАЦИЯ: Кешираме parent-child връзките
            self::$categories_parent_cache[$category_id] = $parent_id;
        }

        // Генерираме пълните пътища за всяка категория
        foreach ($categories_data as $category_id => $data) {
            $full_path = $this->_buildCategoryPath($category_id, $categories_data);
            if (!empty($full_path)) {
                self::$categories_cache[$full_path] = $category_id;
            }
        }

        // $this->writeToCronLog("MultiFeed Syncer: Заредени " . count(self::$categories_cache) . " категории в кеша.", 'mfs_categories.log');
    }

    /**
     * Построява пълния път на категория рекурсивно
     * @param int $category_id ID на категорията
     * @param array $categories_data Масив с данни за всички категории
     * @return string Пълен път на категорията
     */
    private function _buildCategoryPath($category_id, $categories_data) {
        if (!isset($categories_data[$category_id])) {
            return '';
        }

        $path_parts = [];
        $current_id = $category_id;

        // Обхождаме нагоре по йерархията
        while ($current_id > 0 && isset($categories_data[$current_id])) {
            array_unshift($path_parts, $categories_data[$current_id]['name']);
            $current_id = $categories_data[$current_id]['parent_id'];
        }

        return implode(' > ', $path_parts);
    }

    /**
     * Изчиства кеша за категории
     * Полезно когато се добавят нови категории по време на синхронизацията
     * @return void
     */
    private function _clearCategoriesCache() {
        self::$categories_cache = [];
        $this->writeToCronLog("MultiFeed Syncer: Кешът за категории е изчистен.", 'mfs_categories.log');
    }

    /**
     * Публичен метод за принудително презареждане на кеша за категории
     * @return void
     */
    public function reloadCategoriesCache() {
        $this->_clearCategoriesCache();
        $this->_loadCategoriesCache();
    }

    /**
     * Извлича всички родителски category_ids за дадена категория включително нея самата
     * ОПТИМИЗИРАНО: Използва кеширани parent-child връзки вместо SQL заявки
     * @param int $category_id ID на категорията
     * @return array Масив с всички category_ids в йерархията (от root до конкретната категория)
     */
    private function _getParentCategoryIds($category_id) {
        $category_ids = [];

        if ($category_id <= 0) {
            return $category_ids;
        }

        // Уверяваме се, че кешът е зареден
        if (empty(self::$categories_parent_cache)) {
            $this->_loadCategoriesCache();
        }

        // Добавяме самата категория
        $category_ids[] = $category_id;

        // ОПТИМИЗАЦИЯ: Използваме кеширани parent-child връзки
        $current_id = $category_id;
        while ($current_id > 0 && isset(self::$categories_parent_cache[$current_id])) {
            $parent_id = self::$categories_parent_cache[$current_id];
            if ($parent_id > 0) {
                // Добавяме родителската категория в началото на масива
                array_unshift($category_ids, $parent_id);
                $current_id = $parent_id;
            } else {
                break;
            }
        }

        return array_unique($category_ids);
    }

    /**
     * Намира OpenCart category_id по пълен път на категорията (оптимизирана версия с кеш)
     * @param string $category_path Пълен път на категорията (напр. "Категория1 > Категория2")
     * @return int ID на категорията или 0 ако не е намерена
     */
    private function _getCategoryIdFromPath($category_path) {
        // Проверка за валиден тип данни
        if (!is_string($category_path) || empty($category_path)) {
            return 0;
        }

        // Уверяваме се, че кешът е зареден
        if (empty(self::$categories_cache)) {
            $this->_loadCategoriesCache();
        }

        // Търсим директно в кеша
        if (isset(self::$categories_cache[$category_path])) {
            return self::$categories_cache[$category_path];
        }

        // Ако не намерим точно съвпадение, връщаме 0

        return 0;
    }

    /**
     * Декодира HTML entities в пътищата на категории
     * @param string $category_path Път на категория
     * @return string Декодиран път
     */
    private function decodeCategoryPath($category_path) {
        if (empty($category_path)) {
            return $category_path;
        }

        // Декодираме HTML entities (особено &gt; -> >, &quot; -> ", &amp; -> &)
        $decoded = html_entity_decode($category_path, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Допълнително почистване на възможни останали entities
        $decoded = str_replace([
            '&gt;',   // > символ
            '&lt;',   // < символ
            '&amp;',  // & символ
            '&quot;', // " символ (двойни кавички)
            '&#39;',  // ' символ (единични кавички)
            '&#34;'   // " символ (алтернативен код)
        ], [
            '>',
            '<',
            '&',
            '"',
            "'",
            '"'
        ], $decoded);

        return trim($decoded);
    }

    /**
     * Безопасно escape-ване на category path за SQL заявки
     * Запазва специални символи като кавички
     * @param string $category_path Път на категория
     * @return string Escape-нат път
     */
    private function safeCategoryEscape($category_path) {
        // Използваме стандартното escape-ване на OpenCart
        return $this->db->escape($category_path);
    }

    /**
     * Тестов метод за проверка на специални символи в category paths
     * @param string $test_category_path Тестов път за проверка
     * @return array Резултати от теста
     */
    public function testSpecialCharacters($test_category_path) {
        $results = [];

        // Оригинален път
        $results['original'] = $test_category_path;

        // След декодиране
        $decoded = $this->decodeCategoryPath($test_category_path);
        $results['decoded'] = $decoded;

        // След escape-ване
        $escaped = $this->safeCategoryEscape($decoded);
        $results['escaped'] = $escaped;

        // Проверка дали съдържа кавички
        $results['contains_quotes'] = strpos($decoded, '"') !== false;

        // Проверка дали съдържа други специални символи
        $results['special_chars'] = [
            'quotes' => substr_count($decoded, '"'),
            'single_quotes' => substr_count($decoded, "'"),
            'ampersand' => substr_count($decoded, '&'),
            'greater_than' => substr_count($decoded, '>'),
            'less_than' => substr_count($decoded, '<')
        ];

        return $results;
    }

    /**
     * Запазва съответствие на категория
     * @param int $mfsc_id ID на конектора
     * @param string $source_category_path Път на категорията от конектора
     * @param string $target_category_path Път на категорията от сайта
     * @return int ID на записа
     */
    public function saveCategoryMapping($mfsc_id, $source_category_path, $target_category_path) {
        // Декодираме HTML entities преди запазване
        $source_category_path = $this->decodeCategoryPath($source_category_path);
        $target_category_path = $this->decodeCategoryPath($target_category_path);

        // Проверяваме дали вече съществува запис
        $existing_query = $this->_executeQuery("
            SELECT `mapping_id` FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
            WHERE `mfsc_id` = '" . (int)$mfsc_id . "'
            AND `source_category_path` = '" . $this->safeCategoryEscape($source_category_path) . "'
        ", false, 'mfs_categories.log');

        if ($existing_query->num_rows) {
            // Актуализираме съществуващия запис
            $this->_executeQuery("
                UPDATE `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
                SET `target_category_path` = '" . $this->safeCategoryEscape($target_category_path) . "',
                    `date_modified` = NOW()
                WHERE `mapping_id` = '" . (int)$existing_query->row['mapping_id'] . "'
            ", false, 'mfs_categories.log');
            return $existing_query->row['mapping_id'];
        } else {
            // Създаваме нов запис
            $this->_executeQuery("
                INSERT INTO `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
                SET `mfsc_id` = '" . (int)$mfsc_id . "',
                    `source_category_path` = '" . $this->safeCategoryEscape($source_category_path) . "',
                    `target_category_path` = '" . $this->safeCategoryEscape($target_category_path) . "',
                    `date_added` = NOW()
            ", false, 'mfs_categories.log');
            return $this->_getLastId();
        }
    }

    /**
     * Запазва или актуализира съответствие между source и target категория с детайлна информация
     * @param int $mfsc_id ID на конектора
     * @param string $source_category_path Път на source категорията
     * @param string $target_category_path Път на target категорията
     * @return array Информация за операцията: ['action' => 'created|updated|unchanged', 'mapping_id' => int]
     */
    public function saveOrUpdateCategoryMapping($mfsc_id, $source_category_path, $target_category_path) {
        // Декодираме HTML entities преди запазване
        $source_category_path = $this->decodeCategoryPath($source_category_path);
        $target_category_path = $this->decodeCategoryPath($target_category_path);

        // Проверяваме дали вече съществува запис
        $existing_query = $this->_executeQuery("
            SELECT `mapping_id`, `target_category_path` FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
            WHERE `mfsc_id` = '" . (int)$mfsc_id . "'
            AND `source_category_path` = '" . $this->safeCategoryEscape($source_category_path) . "'
        ", false, 'mfs_categories.log');

        if ($existing_query->num_rows) {
            $existing_target = $this->decodeCategoryPath($existing_query->row['target_category_path']); // Декодираме и съществуващия
            $mapping_id = $existing_query->row['mapping_id'];

            // Проверяваме дали target_path е различен
            if ($existing_target !== $target_category_path) {
                // Различен target_path - актуализираме
                $this->_executeQuery("
                    UPDATE `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
                    SET `target_category_path` = '" . $this->safeCategoryEscape($target_category_path) . "',
                        `date_modified` = NOW()
                    WHERE `mapping_id` = '" . (int)$mapping_id . "'
                ", false, 'mfs_categories.log');
                return [
                    'action' => 'updated',
                    'mapping_id' => $mapping_id,
                    'old_target' => $existing_target,
                    'new_target' => $target_category_path
                ];
            } else {
                // Същия target_path - няма промени
                return [
                    'action' => 'unchanged',
                    'mapping_id' => $mapping_id,
                    'target' => $target_category_path
                ];
            }
        } else {
            // Не съществува запис - създаваме нов
            $this->_executeQuery("
                INSERT INTO `" . DB_PREFIX . "multi_feed_syncer_categories_mapping`
                SET `mfsc_id` = '" . (int)$mfsc_id . "',
                    `source_category_path` = '" . $this->safeCategoryEscape($source_category_path) . "',
                    `target_category_path` = '" . $this->safeCategoryEscape($target_category_path) . "',
                    `date_added` = NOW()
            ", false, 'mfs_categories.log');
            return [
                'action' => 'created',
                'mapping_id' => $this->_getLastId(),
                'target' => $target_category_path
            ];
        }
    }

    /**
     * Търси категории от сайта за автозавършване
     * @param string $search_term Термин за търсене
     * @param int $limit Максимален брой резултати
     * @return array
     */
    public function searchSiteCategories($search_term, $limit = 10) {

        $search_term = mb_strtolower($search_term);    
        $parts =  $this->split_categories($search_term);
        
        while (count($parts) > 1) {
            $part = array_pop($parts);
            if(!trim($part)) {
                continue;
            }
            $search_term = trim($part);
            break; 
        }
        $search_term = explode(' ', $search_term);
        $search_term = implode('%', $search_term);
        $search_term = $this->db->escape($search_term);


        $query = $this->_executeQuery("
            SELECT cd.`name`, c.`category_id`
            FROM `" . DB_PREFIX . "category` c
            LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (c.`category_id` = cd.`category_id`)
            WHERE cd.`language_id` = '" . (int)$this->config->get('config_language_id') . "'
            AND LOWER(cd.`name`) LIKE '%" . $search_term . "%'
            ORDER BY cd.`name` ASC
            LIMIT " . (int)$limit, false, 'mfs_categories.log'
        );
        return $query->rows;
    }

    /**
 * Разделя стринг по символите > › » и връща масив от категории.
 *
 * @param string $input
 * @return array
 */
private function split_categories($input) {
    // Превеждаме до малки букви за по-добра консистентност
    $input = mb_strtolower($input);

    // Нормализиране: заместваме › и » с >
    $normalized = str_replace(['›', '»', '&gt;'], '>', $input);

    // Разделяме по ">" със или без интервали около него
    $parts = preg_split('/\s*>\s*/u', $normalized);

    // Премахваме празни елементи (ако има двойни стрелки например)
    $parts = array_filter($parts, function($p) {
        return trim($p) !== '';
    });

    // Изчистваме интервалите от началото и края
    $parts = array_map('trim', $parts);

    return array_values($parts); // преиндексиране на масива
}


    /**
     * Получава пълния път на категория
     * @param int $category_id ID на категорията
     * @return string
     */
    public function getCategoryPath($category_id) {
        $path_parts = [];
        $current_id = $category_id;

        while ($current_id > 0) {
            $query = $this->_executeQuery("
                SELECT c.`parent_id`, cd.`name`
                FROM `" . DB_PREFIX . "category` c
                LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (c.`category_id` = cd.`category_id`)
                WHERE c.`category_id` = '" . (int)$current_id . "'
                AND cd.`language_id` = '" . (int)$this->config->get('config_language_id') . "'
            ", false, 'mfs_categories.log');

            if ($query->num_rows) {
                array_unshift($path_parts, $query->row['name']);
                $current_id = $query->row['parent_id'];
            } else {
                break;
            }
        }

        return implode(' > ', $path_parts);
    }

    /**
     * Получава всички активни категории от сайта с техните пътища
     * @return array Масив с категории във формат ['category_id' => 'пълен_път']
     */
    public function getAllSiteCategories() {
        $categories_with_paths = [];

        // Получаваме всички активни категории
        $query = $this->_executeQuery("
            SELECT c.`category_id`, cd.`name`, c.`parent_id`, c.`sort_order`
            FROM `" . DB_PREFIX . "category` c
            LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (c.`category_id` = cd.`category_id`)
            WHERE cd.`language_id` = '" . (int)$this->config->get('config_language_id') . "'
            AND c.`status` = '1'
            ORDER BY c.`sort_order` ASC, cd.`name` ASC
        ", false, 'mfs_categories.log');

        // За всяка категория генерираме пълния път
        foreach ($query->rows as $category) {
            $full_path = $this->getCategoryPath($category['category_id']);
            if (!empty($full_path)) {
                $categories_with_paths[$category['category_id']] = $full_path;
            }
        }

        return $categories_with_paths;
    }

    /**
     * Създава нова OpenCart категория
     * @param string $name Име на категорията
     * @param int $parent_id ID на родителската категория (0 за root)
     * @param int $sort_order Ред на сортиране
     * @return int ID на новосъздадената категория
     */
    public function createNewCategory($name, $parent_id = 0, $sort_order = 0) {
        $language_id = (int)$this->config->get('config_language_id');

        // Създаваме записа в таблицата category
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "category`
            SET `parent_id` = '" . (int)$parent_id . "',
                `sort_order` = '" . (int)$sort_order . "',
                `status` = '1',
                `date_added` = NOW(),
                `date_modified` = NOW()
        ", true, 'mfs_categories.log');

        $category_id = $this->_getLastId();

        // Създаваме записа в таблицата category_description
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "category_description`
            SET `category_id` = '" . (int)$category_id . "',
                `language_id` = '" . $language_id . "',
                `name` = '" . $this->db->escape($name) . "',
                `description` = '" . $this->db->escape($name) . "',
                `meta_title` = '" . $this->db->escape($name) . "',
                `meta_description` = '" . $this->db->escape($name) . "',
                `meta_keyword` = '" . $this->db->escape($name) . "'
        ", true, 'mfs_categories.log');

        // Създаваме SEO URL за категорията
        $seo_keyword = $this->generateSeoKeyword($name);
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "seo_url`
            SET `query` = 'category_id=" . (int)$category_id . "',
                `keyword` = '" . $this->db->escape($seo_keyword) . "'
        ", true, 'mfs_categories.log');

        // Изчистваме кеша за категории, тъй като е добавена нова категория
        $this->_clearCategoriesCache();

        return $category_id;
    }

    /**
     * Генерира SEO keyword от име на категория
     * @param string $name Име на категорията
     * @return string SEO keyword
     */
    private function generateSeoKeyword($name) {
        // Конвертиране на кирилица в латиница
        $transliteration = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ж' => 'zh',
            'з' => 'z', 'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n',
            'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u', 'ф' => 'f',
            'х' => 'h', 'ц' => 'ts', 'ч' => 'ch', 'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y',
            'ю' => 'yu', 'я' => 'ya',
            'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ж' => 'Zh',
            'З' => 'Z', 'И' => 'I', 'Й' => 'Y', 'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N',
            'О' => 'O', 'П' => 'P', 'Р' => 'R', 'С' => 'S', 'Т' => 'T', 'У' => 'U', 'Ф' => 'F',
            'Х' => 'H', 'Ц' => 'Ts', 'Ч' => 'Ch', 'Ш' => 'Sh', 'Щ' => 'Sht', 'Ъ' => 'A', 'Ь' => 'Y',
            'Ю' => 'Yu', 'Я' => 'Ya'
        ];

        // Конвертираме кирилицата
        $keyword = strtr($name, $transliteration);

        // Конвертираме в малки букви
        $keyword = mb_strtolower($keyword, 'UTF-8');

        // Заменяме интервали и специални символи със средно тире
        $keyword = preg_replace('/[^a-z0-9]+/', '-', $keyword);

        // Премахваме тирета от началото и края
        $keyword = trim($keyword, '-');

        // Проверяваме дали keyword-а вече съществува и добавяме номер ако е нужно
        $original_keyword = $keyword;
        $counter = 1;

        while ($this->seoKeywordExists($keyword)) {
            $keyword = $original_keyword . '-' . $counter;
            $counter++;
        }

        return $keyword;
    }

    /**
     * Проверява дали SEO keyword вече съществува
     * @param string $keyword SEO keyword за проверка
     * @return bool
     */
    private function seoKeywordExists($keyword) {
        $query = $this->_executeQuery("
            SELECT COUNT(*) as count
            FROM `" . DB_PREFIX . "seo_url`
            WHERE `keyword` = '" . $this->db->escape($keyword) . "'
        ", false, 'mfs_seo_url.log');

        return $query->row['count'] > 0;
    }

    // ===== Utility Methods =====

    /**
     * Валидира стойността на атрибут - проверява дали не съдържа само тирета и интервали
     * @param string $value Стойност на атрибута
     * @return bool true ако стойността е валидна, false ако трябва да се пропусне
     */
    private function _isValidAttributeValue($value) {
        if (empty($value)) {
            return false;
        }

        // Премахваме интервали и тирета
        $cleaned_value = trim(str_replace(['-', ' ', "\t", "\n", "\r"], '', $value));

        // Ако след премахване на тирета и интервали няма нищо, стойността е невалидна
        if (empty($cleaned_value)) {
            return false;
        }

        return true;
    }

    /**
     * Получава ID-тата на категориите за продукт
     * @param int $product_id ID на продукта
     * @param array $product_data Данни на продукта
     * @return array Масив с category_id
     */
    private function _getProductCategoryIds($product_id, $product_data) {
        $category_ids = [];

        // Ако продуктът вече съществува, вземаме категориите от базата
        if ($product_id && !isset($product_data['categories_data_source'])) {
            $query = $this->_executeQuery("
                SELECT category_id
                FROM `" . DB_PREFIX . "product_to_category`
                WHERE product_id = '" . (int)$product_id . "'
            ", false, 'mfs_product_to_category.log');

            if ($query->rows) {
                foreach ($query->rows as $row) {
                    $category_ids[] = $row['category_id'];
                }
            }
        }

        // Ако има категории от source данните, използваме тях
        if (isset($product_data['categories_data_source']) && !empty($product_data['categories_data_source'])) {
            // Използваме съществуващия механизъм за получаване на category_id от пътя
            foreach ($product_data['categories_data_source'] as $category_path) {
                $category_id = $this->_getCategoryIdFromPath($category_path);
                if ($category_id) {
                    $category_ids[] = $category_id;
                }
            }
        }

        return array_unique($category_ids);
    }

    // ===== Standard OpenCart Attributes Integration Methods =====

    /**
     * Предварително зарежда всички атрибутни групи и атрибути в паметта за оптимизация
     * @param int $language_id ID на езика
     */
    private function _preloadAllAttributes($language_id) {
        if ($this->attributes_preloaded) {
            return; // Вече са заредени
        }

        $this->writeToCronLog("MultiFeed Syncer Standard Attributes: Започва предварително зареждане на атрибути...", 'mfs_attributes.log');

        // Зареждане на всички атрибутни групи
        $groups_query = $this->_executeQuery("
            SELECT ag.attribute_group_id, agd.name, agd.language_id
            FROM `" . DB_PREFIX . "attribute_group` ag
            LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd
                ON (ag.attribute_group_id = agd.attribute_group_id)
            WHERE agd.language_id = '" . (int)$language_id . "'
        ", false, 'mfs_attributes.log');

        $groups_count = 0;
        foreach ($groups_query->rows as $group) {
            // ОПТИМИЗАЦИЯ: Използваме lowercase нормализирано име вместо MD5 хеш
            $normalized_name = mb_strtolower($group['name']);
            $cache_key = $normalized_name . '_' . $group['language_id'];
            $this->preloaded_attribute_groups[$cache_key] = $group['attribute_group_id'];
            $groups_count++;
        }

        // Зареждане на всички атрибути
        $attributes_query = $this->_executeQuery("
            SELECT a.attribute_id, a.attribute_group_id, ad.name, ad.language_id
            FROM `" . DB_PREFIX . "attribute` a
            LEFT JOIN `" . DB_PREFIX . "attribute_description` ad
                ON (a.attribute_id = ad.attribute_id)
            WHERE ad.language_id = '" . (int)$language_id . "'
        ", false, 'mfs_attributes.log');

        $attributes_count = 0;
        $debug_keys_sample = []; // За debug на първите няколко ключа

        foreach ($attributes_query->rows as $attribute) {
            // ОПТИМИЗАЦИЯ: Използваме lowercase нормализирано име вместо MD5 хеш
            $normalized_name = mb_strtolower($attribute['name']);

            // СТАР КЕШ: Пълен ключ с group_id (за съвместимост)
            $full_cache_key = $normalized_name . '_' . $attribute['attribute_group_id'] . '_' . $attribute['language_id'];
            $this->preloaded_attributes[$full_cache_key] = $attribute['attribute_id'];

            // НОВ ОПРОСТЕН КЕШ: Само име и език (БЕЗ group_id)
            $simple_cache_key = $normalized_name . '_' . $attribute['language_id'];
            $this->simplified_attributes_cache[$simple_cache_key] = [
                'attribute_id' => $attribute['attribute_id'],
                'group_id' => $attribute['attribute_group_id'],
                'original_name' => $attribute['name']
            ];

            $attributes_count++;

            // Събираме sample за debug
            if ($attributes_count <= 20) {
                $debug_keys_sample[] = [
                    'original_name' => $attribute['name'],
                    'normalized_name' => $normalized_name,
                    'group_id' => $attribute['attribute_group_id'],
                    'language_id' => $attribute['language_id'],
                    'full_cache_key' => $full_cache_key,
                    'simple_cache_key' => $simple_cache_key,
                    'attribute_id' => $attribute['attribute_id']
                ];
            }

            // Детайлно логиране на първите 10 атрибута за debug
            if ($attributes_count <= 10) {
                $this->writeToCronLog("MultiFeed Syncer PRELOAD FIXED: Зареден атрибут '{$attribute['name']}' (group_id: {$attribute['attribute_group_id']}) с опростен ключ '{$simple_cache_key}' и ID {$attribute['attribute_id']}", 'mfs_attributes.log');
            }
        }

        // DEBUG: Логиране на sample от заредените ключове
        $this->writeToCronLog("MultiFeed Syncer PRELOAD DEBUG SAMPLE: Първите " . count($debug_keys_sample) . " заредени атрибута:", 'mfs_attributes.log');
        foreach ($debug_keys_sample as $sample) {
            $this->writeToCronLog("  - '{$sample['original_name']}' -> '{$sample['simple_cache_key']}' (ID: {$sample['attribute_id']})", 'mfs_attributes.log');
        }

        $this->attributes_preloaded = true;
        $this->writeToCronLog("MultiFeed Syncer Standard Attributes OPTIMIZED: Предварително заредени {$groups_count} атрибутни групи и {$attributes_count} атрибута с нормализирани ключове.", 'mfs_attributes.log');
        $this->writeToCronLog("MultiFeed Syncer PRELOAD OPTIMIZED: Общо заредени в кеша " . count($this->preloaded_attributes) . " атрибута", 'mfs_attributes.log');

        // DEBUG: Статистика по атрибутни групи
        $this->_logAttributeGroupStatistics();
    }

    /**
     * Логира статистика за заредените атрибути по групи за debug
     */
    private function _logAttributeGroupStatistics() {
        $group_stats = [];
        $sample_attributes = [];

        // Анализираме заредените атрибути по групи
        foreach ($this->preloaded_attributes as $cache_key => $attribute_id) {
            $parts = explode('_', $cache_key);
            if (count($parts) >= 3) {
                $group_id = $parts[count($parts) - 2]; // Предпоследният елемент
                $language_id = $parts[count($parts) - 1]; // Последният елемент
                $attribute_name = implode('_', array_slice($parts, 0, -2)); // Всичко освен последните 2

                if (!isset($group_stats[$group_id])) {
                    $group_stats[$group_id] = 0;
                }
                $group_stats[$group_id]++;

                // Събираме sample атрибути за всяка група
                if (!isset($sample_attributes[$group_id])) {
                    $sample_attributes[$group_id] = [];
                }
                if (count($sample_attributes[$group_id]) < 3) {
                    $sample_attributes[$group_id][] = $attribute_name;
                }
            }
        }

        $this->writeToCronLog("MultiFeed Syncer PRELOAD STATS: Статистика по атрибутни групи:", 'mfs_attributes.log');
        foreach ($group_stats as $group_id => $count) {
            $samples = isset($sample_attributes[$group_id]) ? implode(', ', $sample_attributes[$group_id]) : 'няма';
            $this->writeToCronLog("  - Група {$group_id}: {$count} атрибута (примери: {$samples})", 'mfs_attributes.log');
        }
    }

    /**
     * Обработва атрибутите на продукт за стандартната OpenCart атрибутна система
     * @param int $product_id ID на продукта
     * @param array $attributes_data Масив с атрибути от source данните
     * @param int $language_id ID на езика
     * @param int $mfsc_id ID на конектора
     */
    private function _processStandardProductAttributes($product_id, $attributes_data, $language_id, $mfsc_id) {
        if (empty($attributes_data)) {
            return;
        }

        $max_attributes = 50; // Лимит за атрибути на продукт
        $processed_count = 0;
        $skipped_count = 0;

        $this->writeToCronLog("MultiFeed Syncer Standard Attributes OPTIMIZED: Обработка на атрибути за продукт ID {$product_id}. Общо атрибути: " . count($attributes_data), 'mfs_attributes.log');

        // Намиране или създаване на основна атрибутна група
        $attribute_group_id = $this->_getOrCreateAttributeGroup('Характеристики', $language_id);

        if (!$attribute_group_id) {
            $this->writeToCronLog("MultiFeed Syncer Standard Attributes: Грешка при създаване на атрибутна група за продукт ID {$product_id}", 'mfs_attributes.log');
            return;
        }

        $attributes_to_insert = [];

        // ОПТИМИЗАЦИЯ: Събираме всички имена на атрибути за batch обработка
        $attribute_names = [];
        $valid_attributes = [];

        foreach ($attributes_data as $attribute) {
            if ($processed_count >= $max_attributes) {
                $this->writeToCronLog("MultiFeed Syncer Standard Attributes: Достигнат лимит от {$max_attributes} атрибута за продукт ID {$product_id}");
                break;
            }

            if (empty($attribute['name']) || empty($attribute['value'])) {
                $skipped_count++;
                continue;
            }

            // Валидация на стойността на атрибута
            if (!$this->_isValidAttributeValue($attribute['value'])) {
                $skipped_count++;
                continue;
            }

            $attribute_names[] = $attribute['name'];
            $valid_attributes[] = $attribute;
            $processed_count++;
        }

        // КРИТИЧНО ОПТИМИЗАЦИЯ: Използваме новия интелигентен механизъм
        if (!empty($attribute_names)) {
            $this->writeToCronLog("MultiFeed Syncer Standard Attributes CRITICAL FIX: Обработка на " . count($attribute_names) . " атрибута с интелигентен кеш", 'mfs_attributes.log');

            // Използваме новия интелигентен метод за получаване на атрибути
            $attribute_ids_map = $this->_smartGetOrCreateAttributes($attribute_names, $attribute_group_id, $language_id);

            // Подготвяме данните за директно записване
            $product_attributes = [$product_id => []];
            foreach ($valid_attributes as $attribute) {
                if (isset($attribute_ids_map[$attribute['name']])) {
                    $product_attributes[$product_id][$attribute['name']] = $attribute['value'];
                }
            }

            // Директно записване с проверка за съществуващи атрибути
            $inserted_count = $this->_directBatchInsertProductAttributes($product_attributes, $attribute_ids_map, $language_id);
            $this->writeToCronLog("MultiFeed Syncer Standard Attributes SUPER OPTIMIZED: Записани {$inserted_count} нови атрибута за продукт ID {$product_id}", 'mfs_attributes.log');
        }

        // Логиране на пропуснатите атрибути
        if ($skipped_count > 0) {
            $this->writeToCronLog("MultiFeed Syncer Standard Attributes: Пропуснати {$skipped_count} атрибута за продукт ID {$product_id} (празни или само тирета)", 'mfs_attributes.log');
        }
    }

    /**
     * Намира или създава атрибутна група
     * @param string $group_name Име на групата
     * @param int $language_id ID на езика
     * @return int|false ID на групата или false при грешка
     */
    private function _getOrCreateAttributeGroup($group_name, $language_id) {
        // Проверка в стария кеш с нормализиран ключ
        $normalized_name = mb_strtolower($group_name);
        $cache_key = $normalized_name . '_' . $language_id;
        if (isset($this->attribute_groups_cache[$cache_key])) {
            return $this->attribute_groups_cache[$cache_key];
        }

        // Проверка в предварително заредения кеш с нормализиран ключ
        if (isset($this->preloaded_attribute_groups[$cache_key])) {
            $group_id = $this->preloaded_attribute_groups[$cache_key];
            $this->attribute_groups_cache[$cache_key] = $group_id;
            return $group_id;
        }

        // Ако не е намерена в кеша, търсене в базата данни (fallback за нови групи)
        $query = $this->_executeQuery("
            SELECT ag.attribute_group_id
            FROM `" . DB_PREFIX . "attribute_group` ag
            LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd
                ON (ag.attribute_group_id = agd.attribute_group_id)
            WHERE agd.name = '" . $this->db->escape($group_name) . "'
                AND agd.language_id = '" . (int)$language_id . "'
            LIMIT 1
        ", false, 'mfs_attributes.log');

        if ($query->num_rows) {
            $group_id = $query->row['attribute_group_id'];
            $this->attribute_groups_cache[$cache_key] = $group_id;
            $this->preloaded_attribute_groups[$cache_key] = $group_id; // Добавяме в предварително заредения кеш
            return $group_id;
        }

        // Създаване на нова група
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "attribute_group`
            SET `sort_order` = '0'
        ", true, 'mfs_attributes.log');

        $group_id = $this->_getLastId();

        if ($group_id) {
            // Създаване на описанието
            $this->_executeQuery("
                INSERT INTO `" . DB_PREFIX . "attribute_group_description`
                SET `attribute_group_id` = '" . (int)$group_id . "',
                    `language_id` = '" . (int)$language_id . "',
                    `name` = '" . $this->db->escape($group_name) . "'
            ", false, 'mfs_attributes.log');

            // Автоматично обновяване на всички кешове с нормализиран ключ
            $normalized_name = mb_strtolower($group_name);
            $cache_key = $normalized_name . '_' . $language_id;
            $this->attribute_groups_cache[$cache_key] = $group_id;
            $this->preloaded_attribute_groups[$cache_key] = $group_id;
            $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED AUTO-UPDATE: Създадена и добавена в кеша нова атрибутна група '{$group_name}' с нормализиран ключ '{$cache_key}' и ID {$group_id}", 'mfs_attributes.log');
            return $group_id;
        }

        return false;
    }

    /**
     * Намира или създава атрибут
     * @param string $attribute_name Име на атрибута
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return int|false ID на атрибута или false при грешка
     */
    private function _getOrCreateAttribute($attribute_name, $attribute_group_id, $language_id) {
        // Проверка в стария кеш с нормализиран ключ
        $normalized_name = mb_strtolower($attribute_name);
        $cache_key = $normalized_name . '_' . $attribute_group_id . '_' . $language_id;
        if (isset($this->attributes_cache[$cache_key])) {
            return $this->attributes_cache[$cache_key];
        }

        // Проверка в предварително заредения кеш с нормализиран ключ
        if (isset($this->preloaded_attributes[$cache_key])) {
            $attribute_id = $this->preloaded_attributes[$cache_key];
            $this->attributes_cache[$cache_key] = $attribute_id;
            return $attribute_id;
        }

        // Ако не е намерен в кеша, търсене в базата данни (fallback за нови атрибути)
        $query = $this->_executeQuery("
            SELECT a.attribute_id
            FROM `" . DB_PREFIX . "attribute` a
            LEFT JOIN `" . DB_PREFIX . "attribute_description` ad
                ON (a.attribute_id = ad.attribute_id)
            WHERE ad.name = '" . $this->db->escape($attribute_name) . "'
                AND ad.language_id = '" . (int)$language_id . "'
                AND a.attribute_group_id = '" . (int)$attribute_group_id . "'
            LIMIT 1
        ", false, 'mfs_attributes.log');

        if ($query->num_rows) {
            $attribute_id = $query->row['attribute_id'];
            $this->attributes_cache[$cache_key] = $attribute_id;
            $this->preloaded_attributes[$cache_key] = $attribute_id; // Добавяме в предварително заредения кеш
            return $attribute_id;
        }

        // Създаване на нов атрибут
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "attribute`
            SET `attribute_group_id` = '" . (int)$attribute_group_id . "',
                `sort_order` = '0'
        ", false, 'mfs_attributes.log');

        $attribute_id = $this->_getLastId();

        if ($attribute_id) {
            // Създаване на описанието
            $this->_executeQuery("
                INSERT INTO `" . DB_PREFIX . "attribute_description`
                SET `attribute_id` = '" . (int)$attribute_id . "',
                    `language_id` = '" . (int)$language_id . "',
                    `name` = '" . $this->db->escape($attribute_name) . "'
            ", false, 'mfs_attributes.log');

            // Автоматично обновяване на всички кешове с нормализиран ключ
            $normalized_name = mb_strtolower($attribute_name);
            $cache_key = $normalized_name . '_' . $attribute_group_id . '_' . $language_id;
            $this->attributes_cache[$cache_key] = $attribute_id;
            $this->preloaded_attributes[$cache_key] = $attribute_id;
            $this->writeToCronLog("MultiFeed Syncer CACHE OPTIMIZED AUTO-UPDATE: Създаден и добавен в кеша нов атрибут '{$attribute_name}' с нормализиран ключ '{$cache_key}' и ID {$attribute_id}", 'mfs_attributes.log');
            return $attribute_id;
        }

        return false;
    }

    /**
     * Пакетно записване на продуктови атрибути
     * @param array $attributes_to_insert Масив с атрибути за записване
     */
    private function _batchInsertProductAttributes($attributes_to_insert) {
        if (empty($attributes_to_insert)) {
            return;
        }

        $values_to_insert = [];
        foreach ($attributes_to_insert as $attr) {
            $values_to_insert[] = "('" . (int)$attr['product_id'] . "', '" . (int)$attr['attribute_id'] . "', '" . (int)$attr['language_id'] . "', '" . $this->db->escape($attr['text']) . "')";
        }

        if (!empty($values_to_insert)) {
            try {
                $this->_executeQuery("INSERT IGNORE INTO `" . DB_PREFIX . "product_attribute` (product_id, attribute_id, language_id, text) VALUES " . implode(', ', $values_to_insert), false, 'mfs_attributes.log');
            } catch (Exception $e) {
                $this->writeToCronLog("MultiFeed Syncer Standard Attributes: Грешка при записване на атрибути: " . $e->getMessage(), 'mfs_attributes.log');
            }
        }
    }

    /**
     * Проверява дали продуктът има записи в стандартната атрибутна система
     * @param int $product_id ID на продукта
     * @return bool
     */
    private function _hasStandardAttributes($product_id) {
        $query = $this->_executeQuery("
            SELECT COUNT(*) as count
            FROM `" . DB_PREFIX . "product_attribute`
            WHERE product_id = '" . (int)$product_id . "'
        ", false, 'mfs_attributes.log');

        return $query->row['count'] > 0;
    }

    /**
     * Изчиства кеша за атрибути (използва се при нужда от рефреш)
     */
    private function _clearAttributesCache() {
        $this->attribute_groups_cache = [];
        $this->attributes_cache = [];
        $this->preloaded_attribute_groups = [];
        $this->preloaded_attributes = [];
        $this->simplified_attributes_cache = [];
        $this->attributes_preloaded = false;

        // Изчистваме и сесийния кеш
        self::$session_created_attributes = [];

        $this->writeToCronLog("MultiFeed Syncer Standard Attributes: Всички кешове за атрибути са изчистени (включително сесийния).", 'mfs_attributes.log');
    }
}
?>