# Корекция на проблема с instance-ите в системата за проследяване на продукти

## Проблем

Системата за проследяване на продукти не регистрираше нито един продукт въпреки че синхронизацията обработваше над 7000 продукта успешно.

### Симптоми:
- Синхронизацията обработва 7000+ продукта успешно
- В логът се записва: "Проследени 0 уникални продукта в цялата синхронизация"
- Системата показва 0 изчезнали продукти
- Няма актуализиране на таблицата за проследяване

### Причина:
**Проблем с различни instances на конектора**

1. **Instance A**: Конекторът, който се извиква от контролера и има данните за проследяване
2. **Instance B**: Конекторът, който се зарежда от основния модел в `_trackSyncedProductInConnector()`

Когато основният модел извиква:
```php
$this->load->model('extension/module/multi_feed_syncer_connectors/eoffice');
$this->model_extension_module_multi_feed_syncer_connectors_eoffice->_trackSyncedProduct($product_id);
```

Той създава **нова instance** на конектора, която няма същите данни за проследяване като оригиналната instance.

## Решение

### Използване на статични променливи

Променихме instance променливите в статични, за да се споделят между всички instances на класа:

```php
// ПРЕДИ (instance променливи)
private $current_sync_product_ids = [];
private $product_tracking_enabled = false;

// СЛЕД (статични променливи)
private static $current_sync_product_ids = [];
private static $product_tracking_enabled = false;
```

### Актуализиране на всички методи

Всички методи за проследяване са актуализирани да използват статичните променливи:

```php
// Примери от актуализираните методи
private function _initializeProductTracking($mfsc_id, &$current_log, $log_entry_prefix) {
    self::$current_sync_product_ids = [];
    self::$product_tracking_enabled = true;
    // ...
}

public function _trackSyncedProduct($product_id) {
    if (self::$product_tracking_enabled && !in_array($product_id, self::$current_sync_product_ids)) {
        self::$current_sync_product_ids[] = $product_id;
        // ...
    }
}

private function _finalizeProductTracking($mfsc_id, &$current_log, $log_entry_prefix) {
    if (!self::$product_tracking_enabled) {
        return;
    }
    
    $current_synced_count = count(self::$current_sync_product_ids);
    $disappeared_products = array_diff($previous_synced_products, self::$current_sync_product_ids);
    // ...
}
```

### Добавяне на ресетиране

Добавен е метод за ресетиране на статичните променливи в края на синхронизацията:

```php
private function _resetProductTracking() {
    self::$current_sync_product_ids = [];
    self::$product_tracking_enabled = false;
}
```

## Логика на работа (коригирана)

### 1. Инициализация (Instance A)
- `processSupplierFeed()` се извиква от контролера
- `_initializeProductTracking()` задава `self::$product_tracking_enabled = true`
- Нулира се `self::$current_sync_product_ids = []`

### 2. Проследяване (Instance B)
- Основният модел извиква `_trackSyncedProductInConnector()`
- Зарежда се нова instance на eOffice конектора
- `_trackSyncedProduct()` използва статичните променливи от Instance A
- Продуктите се акумулират в `self::$current_sync_product_ids`

### 3. Финализиране (Instance A)
- `_finalizeProductTracking()` се извиква в края на `processSupplierFeed()`
- Използва акумулираните данни от статичните променливи
- Сравнява с предишните синхронизации и нулира изчезналите продукти

### 4. Ресетиране (Instance A)
- `_resetProductTracking()` подготвя за следващата синхронизация

## Файлове с промени

### `admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`
- **Променени** променливите от instance в статични:
  - `private $current_sync_product_ids` → `private static $current_sync_product_ids`
  - `private $product_tracking_enabled` → `private static $product_tracking_enabled`
- **Актуализирани** всички методи да използват `self::$` вместо `$this->`
- **Добавен** метод `_resetProductTracking()`
- **Добавено** извикване на `_resetProductTracking()` в края на `processSupplierFeed()`

### `admin/model/extension/module/multi_feed_syncer.php`
- **Опростен** метод `_trackSyncedProductInConnector()` (премахнато debug логирането)

## Резултат

Сега системата работи коректно:
- ✅ **Споделя данни** между различните instances чрез статични променливи
- ✅ **Акумулира продукти** от всички batch порции
- ✅ **Проследява всички 7000+ продукта** правилно
- ✅ **Нулира бройки** само на наистина изчезнали продукти
- ✅ **Логира прогреса** на всеки 100 продукта

## Тестване

### Очаквани резултати в логовете:

```
eOffice Connector: Инициализирано проследяване на продукти за конектор ID: 1
eOffice Connector: Проследени 100 продукта до момента
eOffice Connector: Проследени 200 продукта до момента
...
eOffice Connector: Проследени 7000 продукта до момента
eOffice Connector: Проследени 7543 уникални продукта в цялата синхронизация.
eOffice Connector: Примерни product_id от текущата синхронизация: 123, 124, 125, ...
eOffice Connector: Намерени 7520 продукта от предишни синхронизации.
eOffice Connector: Сравнение - Предишни: 7520, Текущи: 7543, Изчезнали: 5
eOffice Connector: Намерени 5 продукта които вече не участват в синхронизацията.
eOffice Connector: Нулирани бройки на 5 продукта.
eOffice Connector: Ресетирано проследяване на продукти за следващата синхронизация
```

Проблемът с различните instances е решен чрез използване на статични променливи!
